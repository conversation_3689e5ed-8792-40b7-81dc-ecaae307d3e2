com.yy.gameecology 下包目录结构

├─y2020
├─y2021
├─y2022
└─y2023
    └─act2023101001
        ├─bean
        ├─dao
        ├─service
        └─worker
            ├─controller

1. 先按年份分层，如当前有 y2020,y2021,y2022,y2023

2. 每个具体活动在对应年份下用含活动ID的名字做 顶层包名，格式限定如：act2023101001

3. 原则上 具体活动 顶层包 之间是没有依赖关系的，只能通过复制代码的方式复用其他活动的功能（若一组活动本身就有业务上的依赖关系，活动包间依赖限制可放开）

4. 具体活动包下常规包名：bean、dao、service、worker、和固定的常量类名 C2023101001，未用到的包名可以省，包名下若类太多可再建子包
   其中 worker 放执行入口如：timer、 controller、subscriber、 XxxServer 等，类后缀就是入口类型， 可视情况创建入口类型的包名比如：controller

5. 若无特别命名需求，可无脑使用 Act2023101001Service、Act2023101001Dao、Act2023101001Controller、Act2023101001Timer 等

6. 组件名字只能用 Act2023101001Component，若一个不够或不便，依次用 Act2023101001Component2 ~ Act2023101001Component9, 对应组件ID只能是:
    Act2023101001Component  - 2023101001
    Act2023101001Component2 - 20231010012
    Act2023101001Component3 - 20231010013
    ....
    Act2023101001Component9 - 20231010019

