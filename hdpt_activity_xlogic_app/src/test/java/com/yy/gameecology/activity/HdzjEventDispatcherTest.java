package com.yy.gameecology.activity;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.hdzj.HdzjEventDispatcher;
import com.yy.gameecology.hdzj.bean.EventNotifyParam;
import com.yy.thrift.hdztranking.RoleType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since 2023/7/18 10:40
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=6"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-6.properties"
})
public class HdzjEventDispatcherTest extends BaseTest {
    static {
        System.setProperty("group", "6");
    }

    @Autowired
    private HdzjEventDispatcher hdzjEventDispatcher;

    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private CommonService commonService;

    @Test
    public void executeTest() throws Throwable {
        String json = "{\"actId\":2022046002,\"event\":{\"eventTime\":1689647672015,\"giftId\":\"340104\",\"giftNum\":1,\"jsonMap\":{},\"recvUid\":2632077920,\"sendUid\":50047909,\"seq\":\"useId:_2357904606_0\",\"sid\":1454054224,\"sourceChannel\":\"PC\",\"ssid\":1454054224,\"template\":\"SkillCard\"},\"eventClass\":\"com.yy.gameecology.activity.bean.SendGiftEvent\",\"handlerBeans\":[{\"actComponent\":{\"activityIds\":[2022026001,2022046002,2022066003],\"componentId\":2017,\"myAttrClass\":\"com.yy.gameecology.hdzj.element.component.attr.AnchorStartShowTipsComponentAttr\",\"uniq1UseIndex\":false},\"actId\":2022046002,\"cmptUseInx\":200,\"beanName\":\"anchorStartShowTipsComponent\"}]}";

        EventNotifyParam param = JSON.parseObject(json, EventNotifyParam.class);

        hdzjEventDispatcher.execute(null, param);
    }

    @Test
    public void test(){
        commonService.getUserInfo(1640778600,false);
        MemberInfo memberInfo = memberInfoService.getMemberInfo(500L, RoleType.USER, "1640778600");
    }
}
