package com.yy.gameecology.activity.client.yrpc;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.BaseTest;
import com.yy.thrift.svc_gateway.BatchResp;
import com.yy.thrift.svc_gateway.BroadcastReq;
import com.yy.thrift.svc_gateway.SimpleResp;
import com.yy.thrift.svc_gateway.UnicastReq;
import org.apache.commons.codec.binary.Hex;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/10/11
 */
public class SvcPlatformGatewayClientTest extends BaseTest {

    @Autowired
    SvcPlatformGatewayClient client;

    @Test
    public void unicastToClientTest() throws TException {
        UnicastReq req = new UnicastReq();
        req.setUid(452012556L);
        req.setMessage(Hex.encodeHexString(
                "hello world from unicastToClientTest!".getBytes(StandardCharsets.UTF_8)));
        SimpleResp resp = client.getProxy().unicastToClient(0, req);
        System.out.println("unicastToClient resp:"+resp);
        Assert.assertEquals(0, resp.getCode());
        Assert.assertEquals(0, resp.getData().getResult());
    }

    @Test
    public void batchUnicastToClientTest() throws TException {
        UnicastReq req = new UnicastReq();
        req.setUid(452012556L);
        req.setMessage("hello world1 from batchUnicastToClientTest!");
        ArrayList<UnicastReq> reqs = Lists.newArrayList();
        reqs.add(req);

        UnicastReq req2 = new UnicastReq();
        req2.setUid(452012556L);
        req2.setMessage("hello world2 from batchUnicastToClientTest!");
        reqs.add(req2);
        BatchResp resp = client.getProxy().batchUnicastToClient(0, reqs);
        System.out.println("batchUnicastToClientTest resp:"+resp);
        Assert.assertEquals(0, resp.getCode());
        Assert.assertEquals(reqs.size(), resp.getDatasSize());
        resp.getDatas().forEach(vo-> Assert.assertEquals(0, vo.getResult()));
    }

    @Test
    public void batchBroadcastSubTest() throws TException {
        BroadcastReq req = new BroadcastReq();
        req.setSid(67071592);
        req.setSsid(1705927104);
        req.setMessage("hello world 1 from batchBroadcastSubTest!");
        List<BroadcastReq> reqs = Lists.newArrayList();
        reqs.add(req);

        BroadcastReq req2 = new BroadcastReq();
        req2.setSid(67071592);
        req2.setSsid(1705927104);
        req2.setMessage("hello world 2 from batchBroadcastSubTest!");
        reqs.add(req2);
        BatchResp resp = client.getProxy().batchBroadcastSub(0, reqs);
        System.out.println("batchBroadcastSubTest resp:"+resp);
        Assert.assertEquals(0, resp.getCode());
        Assert.assertEquals(reqs.size(), resp.getDatasSize());
        resp.getDatas().forEach(vo-> Assert.assertEquals(0, vo.getResult()));
    }

    @Test
    public void broadcastSubTest() throws TException {
        BroadcastReq req = new BroadcastReq();
        req.setSid(67071592);
        req.setSsid(1705927104);
        req.setMessage("hello world from broadcastSubTest!");
        SimpleResp resp = client.getProxy().broadcastSub(0, req);
        System.out.println("broadcastSubTest resp:"+resp);
        Assert.assertEquals(0, resp.getCode());
        Assert.assertEquals(0, resp.getData().getResult());
    }

    @Test
    public void batchBroadcastTopTest() throws TException {
        BroadcastReq req = new BroadcastReq();
        req.setSid(67071592);
        req.setMessage("hello world 1 from batchBroadcastTopTest!");
        List<BroadcastReq> reqs = Lists.newArrayList();
        reqs.add(req);

        BroadcastReq req2 = new BroadcastReq();
        req2.setSid(67071592);
        req2.setMessage("hello world 2 from batchBroadcastTopTest!");
        reqs.add(req2);
        BatchResp resp = client.getProxy().batchBroadcastTop(0, reqs);
        System.out.println("batchBroadcastTopTest resp:"+resp);
        Assert.assertEquals(0, resp.getCode());
        Assert.assertEquals(reqs.size(), resp.getDatasSize());
        resp.getDatas().forEach(vo-> Assert.assertEquals(0, vo.getResult()));
    }

    @Test
    public void broadcastTopTest() throws TException {
        BroadcastReq req = new BroadcastReq();
        req.setSid(67071592);
        req.setMessage("hello world from broadcastTopTest!");
        SimpleResp resp = client.getProxy().broadcastTop(0, req);
        System.out.println("broadcastTopTest resp:"+resp);
        Assert.assertEquals(0, resp.getCode());
        Assert.assertEquals(0, resp.getData().getResult());
    }



}
