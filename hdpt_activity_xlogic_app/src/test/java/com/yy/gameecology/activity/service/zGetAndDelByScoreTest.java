package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static java.util.Collections.singletonList;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-03-05 16:46
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-2.properties"})
public class zGetAndDelByScoreTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ActRedisGroupDao actRedisDao;

    private static final String Z_GET_AND_DEL_BY_SCORE_LUA = "zGetAndDelByScore.lua";

    @Test
    public void test() {
        //Zrangebyscore hdzt_ranking:2021032002:26:_:_:_|20 0 1000 limit 0 1
        while (true) {
            List<String> keys = singletonList("yuezhan_gift_event_seq");
            List<String> argv = Lists.newArrayList("0","1615155671460","1");
            try {
                String result = actRedisDao.executeLua(RedisConfigManager.OLD_ACT_GROUP_CODE, Z_GET_AND_DEL_BY_SCORE_LUA, String.class, keys, argv);
                log.info(" result:{}", result);
            }catch (Exception e){
                log.error("",e);
            }

        }
    }




}
