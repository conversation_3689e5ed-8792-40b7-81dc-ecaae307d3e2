package com.yy.gameecology.common.db.model.gameecology;

import org.springframework.jdbc.core.RowMapper;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/19 10:17
 **/
public class HdzjComponentDoc implements java.io.Serializable, Cloneable {
    public HdzjComponentDoc() {

    }

    public static String TABLE_NAME = "hdzj_component_doc";

    // java 属性字段 和 库表字段对应关系， key-java属性， val-表字段名
    public static Map<String, String> TABLE_FIELDS = new HashMap<>();

    static {
        TABLE_FIELDS.put("componentId", "componentId");
        TABLE_FIELDS.put("docJson", "docJson");
        TABLE_FIELDS.put("updateTime", "updateTime");
    }

    public static RowMapper<HdzjComponentDoc> ROW_MAPPER = (rs, rowNum) -> {
        HdzjComponentDoc entity = new HdzjComponentDoc();
        entity.setDocJson(rs.getString("docJson"));
        entity.setComponentId(rs.getLong("componentId"));
        entity.setUpdateTime(rs.getDate("updateTime"));


        return entity;
    };

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{
            "componentId"
    };

    private Long componentId;
    private String docJson;
    private Date updateTime;

    public Long getComponentId() {
        return componentId;
    }

    public void setComponentId(Long componentId) {
        this.componentId = componentId;
    }

    public String getDocJson() {
        return docJson;
    }

    public void setDocJson(String docJson) {
        this.docJson = docJson;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
