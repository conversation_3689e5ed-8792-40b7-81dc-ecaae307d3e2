package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-21 20:41
 **/
@Data
@TableColumn(underline = true)
public class Cmpt5150UserPuzzle implements Serializable {

   private static final long serialVersionUID = 1L;

   public static String TABLE_NAME = "cmpt_5150_user_puzzle";


   // 表主键属性集合
   public static final String[] TABLE_PKS = new String[]{"id"};

   public static RowMapper<Cmpt5150UserPuzzle> ROW_MAPPER = null;

   /**
    * id
    */
   private Long id;

   /**
    * 活动id
    */
   private Long actId;

   /**
    * cmpt_use_inx
    */
   private Integer cmptUseInx;

   /**
    * uid
    */
   private Long userUid;

   private Long anchorUid;

   /**
    * puzzle_code
    */
   private String puzzleCode;

   /**
    * balance
    */
   private Long balance;

   /**
    * create_time
    */
   private Date createTime;

   /**
    * update_time
    */
   private Date updateTime;
}
