package com.yy.gameecology.common.db.mapper.gameecology.cmpt;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface Cmpt5129PushUserInfoMapper {

    @Select("""
            select uid from cmpt_5129_push_user_info_${actId} 
            where `uid` > #{startUid}
            and `close_push` = 0 
            and `push_amount` < #{pushAmount} 
            and `last_complete_task_time` <  #{lastCompleteTaskTime} 
            and `push_time` < #{pushTime} 
            order by uid limit #{offset} ,#{limit}
            """)
    List<Long> selectPushMembers(@Param("actId") long actId,
                                 @Param("pushTime") Date pushTime,
                                 @Param("lastCompleteTaskTime") Date lastCompleteTaskTime,
                                 @Param("pushAmount") long pushAmount,
                                 @Param("startUid") long startUid,
                                 @Param("offset") long offset,
                                 @Param("limit") long limit);
}
