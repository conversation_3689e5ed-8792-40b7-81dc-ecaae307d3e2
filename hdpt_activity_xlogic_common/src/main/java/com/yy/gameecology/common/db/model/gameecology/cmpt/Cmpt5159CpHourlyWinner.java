package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.db.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * CP小时第一名记录表
 * 用于存储每小时CP榜单的第一名信息
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Data
@TableColumn(underline = true)
public class Cmpt5159CpHourlyWinner implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "cmpt_5159_cp_hourly_winner";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<Cmpt5159CpHourlyWinner> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5159CpHourlyWinner result = new Cmpt5159CpHourlyWinner();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setCmptUseInx(rs.getInt("cmpt_use_inx"));
        result.setSeq(rs.getString("seq"));
        result.setHourCode(rs.getString("hour_code"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setUserNick(rs.getString("user_nick"));
        result.setAnchorNick(rs.getString("anchor_nick"));
        result.setUserAvatar(rs.getString("user_avatar"));
        result.setAnchorAvatar(rs.getString("anchor_avatar"));
        result.setScore(rs.getLong("score"));
        result.setSid(rs.getLong("sid"));
        result.setSsid(rs.getLong("ssid"));
        result.setGiftPoolRemain(rs.getLong("gift_pool_remain"));
        result.setAwardAmount(rs.getLong("award_amount"));
        result.setAwardType(rs.getInt("award_type"));
        result.setCreateTime(rs.getTimestamp("create_time"));
        result.setUpdateTime(rs.getTimestamp("update_time"));
        return result;
    };

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long actId;

    /**
     * 组件使用索引
     */
    private Integer cmptUseInx;

    /**
     * 幂等性序列号
     */
    private String seq;

    /**
     * 小时编码，格式：yyyyMMddHH
     */
    private String hourCode;

    /**
     * 用户UID
     */
    private Long userUid;

    /**
     * 主播UID
     */
    private Long anchorUid;

    /**
     * 用户昵称
     */
    private String userNick;

    /**
     * 主播昵称
     */
    private String anchorNick;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 主播头像
     */
    private String anchorAvatar;

    /**
     * 第一名分数
     */
    private Long score;

    /**
     * 房间SID
     */
    private Long sid;

    /**
     * 房间SSID
     */
    private Long ssid;

    /**
     * 奖池剩余金额（分为单位）
     */
    private Long giftPoolRemain;

    /**
     * 实际发放奖励金额（分为单位）
     */
    private Long awardAmount;

    /**
     * 奖励类型：1-礼物奖励，2-进场秀奖励
     */
    private Integer awardType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
