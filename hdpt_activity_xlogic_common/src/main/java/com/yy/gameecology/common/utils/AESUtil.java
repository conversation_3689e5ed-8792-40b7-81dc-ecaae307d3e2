package com.yy.gameecology.common.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.map.LRUMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.Key;
import java.util.Base64;

/**
 * This class is used for ...
 * AES Coder<br/>
 * secret key length:   128bit, default:    128 bit<br/>
 * Generated through md5.
 * mode:    ECB/CBC/PCBC/CTR/CTS/CFB/CFB8 to CFB128/OFB/OBF8 to OFB128<br/>
 * padding: Nopadding/PKCS5Padding/ISO10126Padding/
 *
 *Improvement:If want to improve performence,you could cache Cipher to reduce the time of producing the Cipher.
 * @version
 *       1.0
 *
 *       1.1 Implements improvement by apache collections LRUMap.
 * <AUTHOR>
 * @time 2011-11-16 下午05:16:01
 */
public class AESUtil {
	public static final Logger logger = LoggerFactory.getLogger(AESUtil.class);
	/**
	 * use password to encrypt content by AES(128bit).
	 * @param content	you want to encrypt message.
	 * @param password	the encrypt key you want to use.
	 * @return the content after encrypted.
	 */
	public static String encrypt(String content, String password){
		return encrypt(content, password, "UTF-8");
	}
	public static String encrypt(String content, String password, String encode){
		try {
			byte[] ptext = content.getBytes(encode);
			byte[] ctext = AESUtil.getEncryptCipher(password, encode).doFinal(ptext);
			return byte2hex(ctext);
		} catch(Exception e) {
			throw new CipherException(e);
		}
	}
	/**
	 * use password to decrypt content by AES(128bit).
	 * @param content	you want to decrypt content.
	 * @param password  the you want to use.
	 * @return	the content after decrypted.
	 */
	public static String decrypt(String content, String password) {
		return decrypt(content, password, "UTF-8");
	}
	public static String decrypt(String content, String password, String encode) {
		try {
			byte[] ptext = AESUtil.getDecryptCipher(password, encode).doFinal(hex2byte(content));
			return new String(ptext, encode);
		} catch(Exception e) {
			throw new CipherException(e);
		}
	}

	private static final int MAX_SIZE = 100;
	private static LRUMap cacheEncryptCipher = new LRUMap(MAX_SIZE);
	private static LRUMap cacheDecryptCipher = new LRUMap(MAX_SIZE);
	private static Cipher getEncryptCipher(String password, String encode) {
		try {
			Cipher cp = (Cipher) cacheEncryptCipher.get(password);
			if(cp == null) {
				Key key = AESUtil.getKey(password, encode);
				cp = Cipher.getInstance("AES");
				cp.init(Cipher.ENCRYPT_MODE, key);

				cacheEncryptCipher.put(password, cp);
			}
			return cp;
		} catch (Exception e) {
			throw new CipherException(e);
		}
	}
	private static Cipher getDecryptCipher(String password, String encode) {
		try {
			Cipher cp = (Cipher) cacheDecryptCipher.get(password);
			if(cp == null) {
				Key key = AESUtil.getKey(password, encode);
				cp = Cipher.getInstance("AES");
				cp.init(Cipher.DECRYPT_MODE, key);

				cacheDecryptCipher.put(password, cp);
			}
			return cp;
		} catch (Exception e) {
			throw new CipherException(e);
		}
	}
	public static class CipherException extends RuntimeException {
		private static final long serialVersionUID = -7938919648349659765L;

		public CipherException(Exception e) {
	        super(e);
	    }
	}

	private static Key getKey(String publickey, String encode) throws UnsupportedEncodingException {
		byte[] bytes = hex2byte(DigestUtils.md5Hex(publickey));
		//byte[] bytes = hex2byte(publickey);
		//byte[] bytes = publickey.getBytes(encode);
		return new SecretKeySpec(bytes, "AES");
	}
	public static byte[] hex2byte(String strhex) {
		if (strhex == null) {
			return null;
		}
		int l = strhex.length();
		final int two = 2;
		if (l % two != 0) {
			return null;
		}
		byte[] b = new byte[l / 2];
		for (int i = 0; i != l / two; i++) {
			b[i] = (byte) Integer.parseInt(strhex.substring(i * 2, i * 2 + 2), 16);
		}
		return b;
	}

	public static String byte2hex(byte b[]) {
		StringBuilder sb = new StringBuilder();
		for (int n = 0; n < b.length; n++) {
			String stmp = Integer.toHexString(b[n] & 0xff);
			if (stmp.length() == 1) {
				sb.append("0");
			}
			sb.append(stmp);
		}
		return sb.toString().toUpperCase();
	}

	public static void main1(String[] args) {
		String key = "1234567890123456";
		String content = "hello world!世界你好！";
		String encode = AESUtil.encrypt(content, key, "GBK");
		String decode = AESUtil.decrypt(encode, key, "GBK");
		System.out.println("密钥:" + key + "\n原文:" + content + "\n\n密文:" + encode + "\n解密:" + decode);
	}

	public static void main(String[] args) throws Exception {
		//秘钥
		String key = "6406808F5434469D";
		//向量
		String iv = "1513D520B9C1459C";

		String enMobile = "kD9zJtyN4vIwJVXJUNHeKA==";
		String mobile = "17788712570";
		String encryptCBC = encryptCBC(mobile, key, iv);
		System.out.println("encryptCBC->"+encryptCBC);
		String decryptCBC = decryptCBC(encryptCBC, key, iv);
		System.out.println("decryptCBC->"+decryptCBC);

		System.out.println("enmobile->" + decryptCBC(encryptCBC, key, iv));
	}


	public static String encryptCBC(String content, String aesKey, String iv) throws Exception {
		byte[] raw = aesKey.getBytes();
		SecretKeySpec secretKeySpec = new SecretKeySpec(raw, "AES");
		//算法,模式,补码方式
		Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
		IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes());
		cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
		byte[] encrypted = cipher.doFinal(content.getBytes());
		return Base64.getEncoder().encodeToString(encrypted);
	}

	public static String decryptCBC(String content, String key,String iv) {
		try {
			content = content.replace("%2B", "+");
			content = content.replace("%2F", "/");
			byte[] raw = key.getBytes("utf-8");
			SecretKeySpec secretKeySpec = new SecretKeySpec(raw, "AES");
			Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
			IvParameterSpec ips = new IvParameterSpec(iv.getBytes());
			cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ips);
			byte[] encrypted1 =  Base64.getDecoder().decode(content);
			byte[] original = cipher.doFinal(encrypted1);
			return new String(original);
		} catch (Exception e) {
			logger.error("decodeMobile error, msg:{}", e.getMessage(), e);
		}
		return "";
	}
}
