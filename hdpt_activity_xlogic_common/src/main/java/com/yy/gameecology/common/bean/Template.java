package com.yy.gameecology.common.bean;

/**
 * <AUTHOR> 2019/8/23
 */
public enum Template {

    any("全部", 0)
    , gamebaby("宝贝", 3)
    , y<PERSON><PERSON><PERSON>("约战", 2)
    , makefriend("交友", 1)
    , all("全部", 4)
    , <PERSON><PERSON><PERSON>("陪玩", 5)
    , <PERSON><PERSON><PERSON>("追玩", 6)
    , yule("娱乐", 7)
    , skillcard("技能卡", 810)
    , unknown("未知", 9999);

    Template(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    private String name;
    private Integer code;

    public String getName() {
        return name;
    }

    public Integer getCode() {
        return code;
    }


    public static Template getTemplate(int templateType) {
        switch (templateType) {
            case 4:
                return all;
            case 3:
                return gamebaby;
            case 2:
                return yuezhan;
            case 1:
                return makefriend;
            case 5:
                return peiwan;
            case 810:
                return skillcard;
            case 9999:
                    return unknown;
            default:
                return all;
        }
    }

    public static Template getTemplateFromThriftEnum(int templateType) {
//        <PERSON><PERSON><PERSON>(1),
//        Gamebaby(2),
//        <PERSON><PERSON><PERSON><PERSON>(3);

        switch (templateType) {
            case 1:
                return yuezhan;
            case 2:
                return gamebaby;
            case 3:
                return makefriend;
            case 810:
                return skillcard;

            default:
                return all;
        }
    }

    public static Template getTemplateByBusi(int busi) {
        switch (busi) {
            case 200:
                return all;
            case 400:
                return gamebaby;
            case 600:
                return yuezhan;
            case 500:
                return makefriend;
            case 900:
                return peiwan;
            case 810:
                return skillcard;

            default:
                return all;
        }
    }
}
