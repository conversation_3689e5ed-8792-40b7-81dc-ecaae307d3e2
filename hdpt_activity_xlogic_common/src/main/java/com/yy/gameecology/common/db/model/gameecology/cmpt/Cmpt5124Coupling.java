package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Getter;
import lombok.Setter;
import org.springframework.jdbc.core.RowMapper;

@Getter
@Setter
@TableColumn(underline = true)
public class Cmpt5124Coupling {

    public static final String TABLE_NAME = "cmpt_5124_coupling";

    public static final String[] TABLE_PKS = new String[]{"id"};

    public static final RowMapper<Cmpt5124Coupling> ROW_MAPPER = (rs, rowNum) -> {
        Cmpt5124Coupling result = new Cmpt5124Coupling();
        result.setId(rs.getLong("id"));
        result.setActId(rs.getLong("act_id"));
        result.setDateCode(rs.getString("date_code"));
        result.setUserUid(rs.getLong("user_uid"));
        result.setAnchorUid(rs.getLong("anchor_uid"));
        result.setCamp(rs.getInt("camp"));

        return result;
    };

    protected Long id;

    protected Long actId;

    protected String dateCode;

    protected Long userUid;

    protected Long anchorUid;

    protected Integer camp;
}
