package com.yy.gameecology.common.cache.memcached;

import com.yy.gameecology.common.cache.Cache;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.util.Assert;

public class MemCacheCache implements Cache {

	protected final Log log = LogFactory.getLog(getClass());

	private final MemCache cache;

	/**
	 * Creates a {@link MemCacheCache} instance.
	 *
	 * @param memCache backing MemCache instance
	 */
	public MemCacheCache(MemCache memCache) {
		Assert.notNull(memCache, "non null MemCache required");
		this.cache = memCache;
	}

	@Override
	public String getName() {
		return cache.getName();
	}

	@Override
	@SuppressWarnings("unchecked")
	public <T> T get(Object key) {
		return (T) cache.get(String.valueOf(key));
	}

	@Override
	public void put(Object key, Object value) {
		if (value == null) {
			log.warn("trying to store a null value to cache under key '" + key + "'");
			return;
		}

		cache.set(String.valueOf(key), value);
	}

	@Override
	public void put(Object key, Object value, long timeToLiveMillis) {
		if (value == null) {
			log.warn("trying to store a null value to cache under key '" + key + "'");
			return;
		}

		cache.set(String.valueOf(key), value, timeToLiveMillis);
	}

	@Override
	public Object remove(Object key) {
		String key1 = String.valueOf(key);
		Object value = cache.get(key1);
		boolean deleteSuc = cache.delete(key1);
		return deleteSuc ? value : deleteSuc;
	}

}
