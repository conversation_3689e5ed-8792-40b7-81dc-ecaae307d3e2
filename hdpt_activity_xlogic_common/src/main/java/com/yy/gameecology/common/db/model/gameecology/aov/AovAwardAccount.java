package com.yy.gameecology.common.db.model.gameecology.aov;

import java.util.Date;

public class AovAwardAccount {
    /**
     * 
     */
    private Long id;

    /**
     * 
     */
    private String account;

    /**
     * 
     */
    private Long uid;

    /**
     * 
     */
    private String mobile;

    /**
     * 
     */
    private Date createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account == null ? null : account.trim();
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}