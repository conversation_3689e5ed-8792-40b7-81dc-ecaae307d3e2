package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-02-18 14:19
 **/
@Data
@TableColumn(underline = true)
public class ComponentDataHash implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "component_data_hash_";

    public static String getTableName(long actId) {
        return TABLE_NAME + actId;
    }

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<ComponentDataHash> ROW_MAPPER = null;

    private Long id;

    /**
     * act_id
     */
    private Long actId;

    /**
     * component_id
     */
    private Long componentId;

    /**
     * cmpt_use_inx
     */
    private Long cmptUseInx;

    /**
     * key_name
     */
    private String keyName;

    /**
     * hash_key_name
     */
    private String hashKeyName;

    /**
     * hash_value
     */
    private String hashValue;

    /**
     * create_time
     */
    private Date createTime;
}
