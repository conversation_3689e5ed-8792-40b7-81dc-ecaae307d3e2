package com.yy.gameecology.common.db.model.gameecology;

import com.yy.gameecology.common.annotation.TableColumn;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * 广播配置表实体类
 * @auther: guojunhui
 * @create: 2020/11/5 16:24
 */

@TableColumn(underline = true)
public class BroadcastTimerConfig implements Serializable {
    public static String TABLE_NAME = "ge_broadcast_timer_config";
    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};
    public static RowMapper<BroadcastTimerConfig> ROW_MAPPER = null;
    private Long id;
    private Long actId;
    private String broadcastTime;
    private String broadcastTxt;
    private String broadcastRanges;
    private Integer status;
    private Date createTime;
    private Date updateTime;
    private Long operateUid;
    private String extJson;

    public BroadcastTimerConfig() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(final Long id) {
        this.id = id;
    }

    public Long getActId() {
        return this.actId;
    }

    public void setActId(final Long actId) {
        this.actId = actId;
    }

    public String getBroadcastTime() {
        return this.broadcastTime;
    }

    public void setBroadcastTime(final String broadcastTime) {
        this.broadcastTime = broadcastTime;
    }

    public String getBroadcastTxt() {
        return this.broadcastTxt;
    }

    public void setBroadcastTxt(final String broadcastTxt) {
        this.broadcastTxt = broadcastTxt;
    }

    public String getBroadcastRanges() {
        return this.broadcastRanges;
    }

    public void setBroadcastRanges(final String broadcastRanges) {
        this.broadcastRanges = broadcastRanges;
    }

    public Integer getStatus() {
        return this.status;
    }

    public void setStatus(final Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(final Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(final Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getOperateUid() {
        return this.operateUid;
    }

    public void setOperateUid(final Long operateUid) {
        this.operateUid = operateUid;
    }

    public String getExtJson() {
        return extJson;
    }

    public void setExtJson(String extJson) {
        this.extJson = extJson;
    }
}
