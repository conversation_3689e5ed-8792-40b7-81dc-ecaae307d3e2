/**
 * Const.java / 2018年8月31日 下午6:35:58
 * <p>
 * Copyright (c) 2018, YY Inc. All Rights Reserved.
 * <p>
 * 郭立平[<EMAIL>]
 */

package com.yy.gameecology.common.consts;

import com.google.common.collect.Maps;
import com.yy.gameecology.common.base.GeIdGenerator;
import com.yy.gameecology.common.base.GeParameterManager;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.exception.SuperException;
import com.yy.gameecology.common.support.MDCTaskDecorator;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import org.apache.dubbo.common.utils.NamedThreadFactory;

import java.util.Date;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 常量定义类-全局就这一个类（除和业务相关但数量众多含义单一的常量，可另起一个文件专门存放，其余常量值应该都放到这里来。
 * 对于数量较少但表示同一类属性值的的多个常量，应该启用内部类的方式限定起来，比如 SYS 就是系统级的公用常量
 *
 * <AUTHOR>
 * @date 2018年8月31日 下午6:35:58
 */
public class Const {
    //udb sa auth for hdpt，http://bd-yylivesdk.yy.com/cloud/account/home.htm

    /**
     * 此APP ID发短信的短信签名是【yy语音】，营销短信发送不再使用这个appid
     */
    public static final String APP_ID = "**********";

    public static final String APP_SECRET = "b09b7bf5_d243";

    public static final int ENT_MAX_TYPE = 6189;

    /**
     * 用于kafka事件通知，无uid的占位，用这个相当于会跳过UID灰度白名单控制，小心使用！！！
     */
    public static final int NONE_UID = -1;

    /**
     * 制作线程池： 达到队列长度后由调用线程程直接处理（这并不保证是最佳办法，因或耗尽调用者线程，请综合评估使用）
     */
    public static ExecutorService makeExecutorService(int corePoolSize) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(corePoolSize, corePoolSize, 0, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), new NamedThreadFactory("makeExecutorService")) {
            @Override
            public void execute(Runnable command) {
                super.execute(MDCTaskDecorator.makeMDCTask(command));
            }
        };
        // 预启动所有核心线程
        //executor.prestartAllCoreThreads();
        return executor;
    }

    public static ScheduledThreadPoolExecutor makeScheduledExecutorService(int corePoolSize) {
        return new ScheduledThreadPoolExecutor(corePoolSize, new NamedThreadFactory("makeScheduledExecutorService")){
            @Override
            public void execute(Runnable task) {
                super.execute(MDCTaskDecorator.makeMDCTask(task));
            }

            @Override
            public ScheduledFuture<?> scheduleWithFixedDelay(Runnable task, long initialDelay, long delay, TimeUnit unit) {
                return super.scheduleWithFixedDelay(MDCTaskDecorator.makeMDCTask(task), initialDelay, delay, unit);
            }

            @Override
            public ScheduledFuture<?> schedule(Runnable command,
                                               long delay,
                                               TimeUnit unit) {
                return super.schedule(MDCTaskDecorator.makeMDCTask(command), delay, unit);
            }
        };
    }

    //////////////////////////////////////////////////////////////////////////
    // 公用线程池，用来异步执行一些非重要操作，这里只给150个线程，对于需要及时完成的任务应该使用专用线程池
//    public static final ExecutorService EXECUTOR_GENERAL = makeExecutorService(150);
    /**
     * 线程池改用yboot的配置线程池
     */
    public static final String GENERAL_POOL = "general";

    //////////////////////////////////////////////////////////////////////////
    // 公用线程池，用来异步执行一些重要操作，这里给100个线程，对于需要及时完成的任务应该使用专用线程池
//    public static final ExecutorService EXECUTOR_IMPORTANT = makeExecutorService(100);

    /**
     * 线程池改用yboot的配置线程池
     */
    public static final String IMPORTANT_POOL = "important";

    /**
     * <pre>
     * 时间计划线程池（用于延迟开始等场合）
     * 不建议用于执行重复定时任务，即不建议使用scheduleAtFixedRate、scheduleWithFixedDelay两个方法，除非以下情况：
     * 1、自己的runnable会捕获一切异常并且不再往外抛出；
     * 2、你已经清楚地知道了如果你的任意一次runnable抛出异常后，可以容忍重复任务不再执行；
     * </pre>
      */
    public static final ScheduledExecutorService EXECUTOR_DELAY_GENERAL = makeScheduledExecutorService(100);

    public static final ScheduledExecutorService EXECUTOR_DELAY_BRO = makeScheduledExecutorService(50);

    //方法日志使用的线程池，可以不执行，不能卡住主线程的执行
//    public static final ExecutorService METHOD_LOG_GENERAL = new ThreadPoolExecutor(
//            10, 150, 100, TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(100) , new ThreadPoolExecutor.AbortPolicy());

    public static final String METHOD_LOG_POOL = "method_log";

    public static final String KAFKA_MSG_HANDLER_POOL = "kafka_msg_handler";

    /**
     *  异步事件处理线程池，不缓存，有线程就执行，没有就使用调用方线程执行
     */
    public static final ExecutorService EXECUTOR_ASYNC_LISTENER = new ThreadPoolExecutor(
            10, 10, 0, TimeUnit.SECONDS, new SynchronousQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    //////////////////////////////////////////////////////////////////////////
    // 公用线程池，用来异步执行一些关键操作（比如需要严格时序的定时器），这里给100个线程
    // 暂时未用，注释节省资源
    //public static final ExecutorService EXECUTOR_VITAL = Executors.newFixedThreadPool(100);

    // 抽奖记录ID生成器
    public static final String GE_LOTTERY_RECORD_ID = "GE_LOTTERY_RECORD_ID";

    // 项目发放ID生成器
    public static final String GE_ITEM_ISSUE_ID = "GE_ITEM_ISSUE_ID";

    /**
     * 里面内容会透传到最终发送方
     */
    public static final String AWARD_ISSUE_EXT ="AWARD_ISSUE_EXT";

    /**
     * 3个基于 mysql 库表的 ID 生成器
     */

    // 构造一个全局 id generator(缺省实现在 SpringBeanAwareFactory.getBean("gameecologyDataSource") 下的  ge_id_generator 表中）
    public static final GeIdGenerator GEIG = GeIdGenerator.getInstance();

    /**
     * 按日期分表，产生的 ID 值上需要体现日期 通过ID本身就能找到该记录所在分表
     * 产生的ID将具有如下格式： yyyyMMddxxxxxx yyyyMMdd - 日期 xxxxxxxx - 8位循环数 （支持每天1亿不重复的值）
     * 实例： 2014030512345678 2014041200092004
     */
    public static long genId(GeIdGenerator IG, String name) {
        return genId(IG, name, new Date());
    }
    public static long genId(GeIdGenerator IG, String name, Date date) {
        try {
            // 控制循环位数为 8 位，支持1亿个值
            long cycle = 100000000;
            long id = IG.getId(name) % cycle;
            long dt = Long.parseLong(DateUtil.format(date, "yyyyMMdd")) * cycle;
            return dt + id;
        } catch (Exception e) {
            throw new SuperException(name + "标识生成失败:" + e.getMessage(), SuperException.E_FAIL_GEN_ID);
        }
    }

    /**
     * 3个基于 mysql 库表的 参数管理器
     */
    // 构造一个全局 parameter manager(缺省实现在 SpringBeanAwareFactory.getBean("gameecologyDataSource") 下的 ge_parameter 表中）
    public static final GeParameterManager GEPM = GeParameterManager.getInstance();

    //udbkey
    public static final String UDB_APPID = "5242";

    public static class SYS {
        //thrift包 最大读字节数, 10M
        public static final int MAX_THRIFT_READ_LEN = 1024 * 1024 * 10;

        // 构造一个全局随机数发生器
        public static Random RD = new Random(System.currentTimeMillis());

        //////////////////////////////////////////////////////////////////////////
        //约战业务特殊需要，批量广播专用线程池，防止影响其它线程工作，不要混用
//        public static final ExecutorService EXECUTOR_BROADCAST = makeExecutorService(50);

        //////////////////////////////////////////////////////////////////////////
        // 公用线程池，用来异步执行一些非重要操作，这里只给100个线程，对于需要及时完成的任务应该使用专用线程池，使用着会很多
//        public static final ExecutorService EXECUTOR_GENERAL = makeExecutorService(100);

        //////////////////////////////////////////////////////////////////////////
        // 公用线程池，用来异步执行一些重要操作，这里给100个线程，对于需要及时完成的任务应该使用专用线程池，使用者应该较少
//        public static final ExecutorService EXECUTOR_IMPORTANT = makeExecutorService(100);

        //////////////////////////////////////////////////////////////////////////
        // 公用线程池，用来异步执行一些关键操作（比如需要严格时序的定时器），这里给100个线程，使用者应该很少
        //public static final ExecutorService EXECUTOR_VITAL = Executors.newFixedThreadPool(100);

        //////////////////////////////////////////////////////////////////////////
        // 使用专用线程池发奖，不和其他线程池共用，防止堵塞其它工作线程
//        public static final ExecutorService EXECUTOR_ISSUE = makeExecutorService(100);

    }

    public static class ID {
        public static final String NG_GAME = "NG_GAME";

        public static final String NG_GAME_TRANS = "NG_GAME_TRANS";
    }

    public static class SUBJECTS{
        /**
         * 挑战票编码
         */
        public static final String TICKET = "TK_001";

        /**
         * 金贝编码
         */
        public static final String JINBEI = "JB_001";
    }

    public static class IMAGE {
        // 默认用户头像
        public static final String DEFAULT_USER_LOGO = "https://web.yystatic.com/public/global/base/image/yybear2.jpg";

        // 默认公会头像
        public static final String DEFAULT_CHANNEL_LOGO = "https://gamebaby.bs2.yy.com/channel.png";
    }

    public static class WEBDB_CHANNEL_COLUMN_NAME{
        public static final String OWUID ="ownerid";
        public static final String ASID ="asid";
        public static final String SID ="sid";
        public static final String SSID ="ssid";
        public static final String NAME ="name";
    }


    //---------------------------redis队列常量-------------------------
    public static class REDISMQ {
        //%s为结构体的uri,根据不同的uri分到不同的redis队列
        public static final String REDIS_MQ_LIST = "redis_mq_queue_%s";

        //%s为结构体的uri,根据不同的uri分到不同的redis队列副本
        public static final String REDIS_MQ_LIST_BAK = "redis_mq_queue_%s_bak";

        //singleConsumer模式下，一条信息只能有一个消费者处理
        public static final String SINGLECONSUMER = "singleConsumer";

        //muchConsumer模式下，一条信息可以有多个消费者处理
        public static final String MUCHCONSUMER = "muchConsumer";

        // 队列消息类型：YY Protocol
        public static final int MSG_TYPE_YYPROTOCOL = 0;

        // 队列消息类型：String
        public static final int MSG_TYPE_STRING = 1;
    }

    // 环境类型
    public static class ENV {
        // 本地调测环境
        public static final String LOCAL = "local";

        // 开发测试环境
        public static final String DEV = "dev";

        // 生产部署环境
        public static final String DEPLOY = "deploy";
    }

    // 请求来源， 1~1000 是约战宝贝方， 1001 ~ 2000 是交友方，其余范围暂不使用， 各方可在自己区段内为内部系统分配明确的值
    public static class GE_SOURCE {
        //游戏宝贝：1~100
        public static final int GAMEBABY_SOURCE_MIN = 1;
        public static final int GAMEBABY_SOURCE_MAX = 100;
        public static final int GAMEBABY_CORE = GAMEBABY_SOURCE_MIN;
        public static final int GAMEBABY_ACTIVITY = 2;

        //约战：101~200
        public static final int YUEZHAN_SOURCE_MIN = 101;
        public static final int YUEZHAN_SOURCE_MAX = 200;
        public static final int YUEZHAN_CORE = YUEZHAN_SOURCE_MIN;
        public static final int YUEZHAN_ACTIVITY = 102;

        //游戏生态：201~300
        public static final int GAMEECOLOGY_SOURCE_MIN = 201;
        public static final int GAMEECOLOGY_SOURCE_MAX = 300;
        public static final int GAMEECOLOGY_CORE = GAMEECOLOGY_SOURCE_MIN;
        public static final int GAMEECOLOGY_ACTIVITY = 202;
        public static final int GAMEECOLOGY_PROXY = GAMEECOLOGY_SOURCE_MAX;

        // 交友那边先假定是这个值（因为目前认为交友只有唯一一个 proxy 进程和我们打交道，这样假设也是没有歧义的）
        public static final int MAKEFRIEND_SOURCE_MIN = 1001;
        public static final int MAKEFRIEND_SOURCE_MAX = 2000;
        public static final int MAKEFRIEND_PROXY = MAKEFRIEND_SOURCE_MAX;

        public static boolean isGamebaby(int source) {
            return source >= GAMEBABY_SOURCE_MIN && source <= GAMEBABY_SOURCE_MAX;
        }

        public static boolean isYuezhan(int source) {
            return source >= YUEZHAN_SOURCE_MIN && source <= YUEZHAN_SOURCE_MAX;
        }

        public static boolean isGameecology(int source) {
            return source >= GAMEECOLOGY_SOURCE_MIN && source <= GAMEECOLOGY_SOURCE_MAX;
        }

        public static boolean isMakefriend(int source) {
            return source >= MAKEFRIEND_SOURCE_MIN && source <= MAKEFRIEND_SOURCE_MAX;
        }
    }

    //////////////// 判断 ge_parameter 参数配置的几个助手函数 - added by guoliping / 2020-10-20 /////////////////
    public static String getGeValue(String prefix, String name, String defVal) {
        String key = StringUtil.isBlank(prefix) ? name : prefix + name;
        String configValue = StringUtil.trim(Const.GEPM.getParamValue(key, defVal));
        return configValue;
    }

    /**
     * 判断标志位是否为 1
     */
    public static boolean isGeOneFlag(String name) {
        return isGeEqual(name, "0", "1");
    }

    public static boolean isGeOneFlag(String name, int defVal) {
        return isGeEqual(name, String.valueOf(defVal), "1");
    }

    /**
     * 判断参数值是否等于期望值
     */
    public static boolean isGeEqual(String name, String defVal, String expectedValue) {
        return isGeEqual(null, name, defVal, expectedValue);
    }

    /**
     * 判断参数值是否等于期望值
     */
    public static boolean isGeEqual(String prefix, String name, String defVal, String expectedValue) {
        String configValue = getGeValue(prefix, name, defVal);
        return configValue.equals(expectedValue);
    }

    /**
     * 为名字添加活动前缀
     * @param actId - 活动ID
     * @param name - 原名字
     * @return 添加了活动前缀后的名字
     */
    public static String addActivityPrefix(long actId, String name) {
        return "act:" + actId + ":" + name;
    }

    /**
     * 为无活动id的key增加redis key 特征前缀
     * 由于活动结束后要清空环境，此类数据不做pika迁移，因此必须带过期时间，且至少在活动结束后7天内过期
     *
     * @param name - 原名字
     * @return act:hdzk:g{group}:{name}
     */
    public static String addActTempPrefix(String name) {
        return "act:hdzk:g" + SysEvHelper.getGroup() + ":" + name;
    }

    /**
     * 加可暴露给外部的配置前缀
     */
    public static String addPublicAttrPrefix(String name) {
        return "public:" + name;
    }

    /**
     * 检查key是否符合活动规则
     * @param actId 活动ID
     * @param key
     * @return
     */
    public static boolean checkActivityKey(long actId, String key) {
        return key.startsWith("act:" + actId+ ":");
    }

    public static final Integer ZERO = 0;
    public static final String ZEROSTR = "0";
    public static final Integer ONE = 1;
    public final static int MIN_ONE = -1;
    public static final int ONE_MINUS = -1;

    public static final int TOW = 2;

    public static final int FIVE = 5;

    public static final int TEN = 10;

    public static final int THIRTY = 30;

    public static final int THREE_HUNDRED = 300;

    public static final String ONESTR = "1";

    public static final String TRUE_STR = "true";
    /////////////////////////////////////////////////////////////////////////////////////////////////

    /** 判断分组 */
    public static final boolean isGroup1() {
        return "1".equals(SysEvHelper.getGroup());
    }
    public static final boolean isGroup2() {
        return "2".equals(SysEvHelper.getGroup());
    }
    public static final boolean isGroup3() {
        return "3".equals(SysEvHelper.getGroup());
    }
    public static final boolean isGroup4() {
        return "4".equals(SysEvHelper.getGroup());
    }

    public static final boolean isGroup7() {
        return "7".equals(SysEvHelper.getGroup());
    }



    // hdzt 的发奖 和 榜单事件消息 seq重复检查 hash key
    // 发奖的
    public static final String HDZT_AWARD_DUPLICATED_CHECK_KEY = "hdzt_award_duplicated_check:%s";
    // 榜单的
    public static final String HDZT_EVENT_DUPLICATED_CHECK_KEY = "hdzt_event_duplicated_check:%s";


    /**
     * 活动组件key 前缀占位符
     */
    public static final String COMPONENT_KEY_SUFFIX = "hdzj_cmpt";

    // 成功码
    public static final int OK_1 = 1;
    public static final int OK_200 = 200;

    public static boolean isOk1(long code) {
        return code == OK_1;
    }

    public static String makeChkMqDupSeq(String queueName, String seq) {
        return queueName + ":" + seq;
    }

    public static final int getActRoute(long actId) {
        return (int) (actId % 10000 / 1000);
    }

    /**
     * 在线子频道信息，为保证系统启动系统，不确保这一定能拿到数据
     */
    private static Map<String, Map<String, String>> ON_LINE_SUB_CHANNEL_INFO_MAP = Maps.newConcurrentMap();

    public static Map<String, String> getSubChannelInfoCache(String sidssid) {
        return ON_LINE_SUB_CHANNEL_INFO_MAP.get(sidssid);
    }

    public static void cacheAllSubChannelInfo(Map<String, Map<String, String>> subChannel) {
        ON_LINE_SUB_CHANNEL_INFO_MAP = subChannel;
    }

    // hdzt_ranking 中 zset 成员榜单查询类型定义
    // 积分
    public static int MEMBER_RANKING_QUERY_VALUE_SCORE           = 0;
    // 排名
    public static int MEMBER_RANKING_QUERY_VALUE_RANK            = 1;
    // 积分+排名
    public static int MEMBER_RANKING_QUERY_VALUE_SCORE_RANK      = 2;
    // 积分+排名+上一名积分
    public static int MEMBER_RANKING_QUERY_VALUE_SCORE_RANK_PREV = 3;

    /**
     * 在线环境（非离线广场）条件注入表达式
     */
    public final static String EXPRESSION_NOT_HISTORY = "environment.getProperty('history') != '1'";

    public final static String EXPRESSION_NOT_LOCAL = "!'local'.equals(environment.getProperty('env'))";

    public static boolean isNumberOrSsid(String args) {
        if(StringUtil.isBlank(args)){
            return false;
        }
        String regex = "^\\d+(\\_\\d+)?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(args);
        return matcher.matches();
    }

    public static CpUid splitCpMember(String member) {
        CpUid cpUid = new CpUid();
        String[] memberArray = member.split("\\|");
        long userId = Convert.toLong(memberArray[0]);
        long anchorId = Convert.toLong(memberArray[1]);
        cpUid.setUserUid(userId);
        cpUid.setAnchorUid(anchorId);
        cpUid.setMember(member);
        return cpUid;
    }

    public static String[] splitCpMemberSimple(String member) {
        return member.split("\\|");
    }

    public static boolean isOne(Integer status) {
        return status != null && status == 1;
    }

}
