package com.yy.gameecology.common.db.model.gameecology.pepc;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;
import org.springframework.jdbc.core.RowMapper;

import java.io.Serializable;
import java.util.Date;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-31 21:22
 **/
@Data
@TableColumn(underline = true)
public class PepcTeam implements Serializable {

    private static final long serialVersionUID = 1L;

    public static String TABLE_NAME = "pepc_team";

    // 表主键属性集合
    public static final String[] TABLE_PKS = new String[]{"id"};

    public static RowMapper<PepcTeam> ROW_MAPPER = null;

    /**
     * id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long actId;

    /**
     * sid
     */
    private Long sid;

    /**
     * ssid
     */
    private Long ssid;

    /**
     * 队长uid
     */
    private Long creator;

    /**
     * 队伍状态 0 组队中 1报名成功 2 赛事取消 3 人数不足 4 队伍过多
     */
    private Integer state;

    /**
     * 1 不公开招募 0 公开招募
     */
    private Integer needApply;

    /**
     * 队伍名称
     */
    private String teamName;

    /**
     * 待审核的队伍名称
     */
    private String teamNameAudit;

    /**
     * 0 审核中 1 审核通过 2 拒绝
     */
    private Integer teamNameAuditState;

    /**
     * 宣言
     */
    private String declaration;

    /**
     * 审批中宣言
     */
    private String declarationAudit;

    /**
     * 0 审核中 1 审核通过 2 拒绝
     */
    private Integer auditState;

    /**
     * 队伍人员数量
     */
    private Integer memberCnt;

    /**
     * 数量限制
     */
    private Integer memberLimit;

    /**
     * 队伍积分，根据积分确认是否轮空
     */
    private Long teamScore;

    /**
     * create_time
     */
    private Date createTime;

    // No-argument constructor
    public PepcTeam() {
    }

    public PepcTeam(Long sid, Long ssid, Long actId, Long creator, Integer state, Integer needApply,
                    String declaration, String declarationAudit, Integer auditState, Integer memberCnt,
                    Integer memberLimit, Date createTime) {
        this.sid = sid;
        this.ssid = ssid;
        this.actId = actId;
        this.creator = creator;
        this.state = state;
        this.needApply = needApply;
        this.declaration = declaration;
        this.declarationAudit = declarationAudit;
        this.auditState = auditState;
        this.memberCnt = memberCnt;
        this.memberLimit = memberLimit;
        this.createTime = createTime;
    }
}
