<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovPhaseTeamMapper">
    <resultMap id="AovPhaseTeamResult" type="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeam">
        <id property="id" column="id" jdbcType="BIGINT" />
        <result property="sid" column="sid" jdbcType="BIGINT" />
        <result property="ssid" column="ssid" jdbcType="BIGINT" />
        <result property="phaseId" column="phase_id" jdbcType="BIGINT" />
        <result property="creator" column="creator" jdbcType="BIGINT" />
        <result property="state" column="state" jdbcType="INTEGER" />
        <result property="needApply" column="need_apply" jdbcType="INTEGER" />
        <result property="teamName" column="team_name" jdbcType="VARCHAR" />
        <result property="teamNameAudit" column="team_name_audit" jdbcType="VARCHAR" />
        <result property="teamNameAuditState" column="team_name_audit_state" jdbcType="INTEGER" />
        <result property="declaration" column="declaration" jdbcType="VARCHAR" />
        <result property="declarationAudit" column="declaration_audit" jdbcType="INTEGER" />
        <result property="auditState" column="audit_state" jdbcType="INTEGER" />
        <result property="memberCnt" column="member_cnt" jdbcType="INTEGER" />
        <result property="memberLimit" column="member_limit" jdbcType="INTEGER" />
        <result property="teamScore" column="team_score" jdbcType="BIGINT" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                sid,
                ssid,
                phase_id,
                creator,
                state,
                need_apply,
                team_name,
                team_name_audit,
                team_name_audit_state,
                declaration,
                declaration_audit,
                audit_state,
                member_cnt,
                member_limit,
                team_score,
                create_time
    </sql>

    <insert id="insertAndGetId" useGeneratedKeys="true" keyProperty="id" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovPhaseTeam">
        INSERT INTO aov_phase_team (sid, ssid, phase_id, creator, state, need_apply,team_name,team_name_audit,team_name_audit_state, declaration, declaration_audit, audit_state, member_cnt, member_limit, team_score, create_time)
        VALUES (#{sid}, #{ssid}, #{phaseId}, #{creator}, #{state}, #{needApply}, #{teamName}, #{teamNameAudit}, #{teamNameAuditState}, #{declaration}, #{declarationAudit}, #{auditState}, #{memberCnt}, #{memberLimit}, #{teamScore}, #{createTime})
    </insert>
    <delete id="deleteTeam">
        delete from aov_phase_team where id = #{teamId}
        and `state` = ${@com.yy.gameecology.common.consts.aov.AovConst$PhaseTeamState@INIT}
    </delete>

    <select id="selectAovPhaseTeam" parameterType="long" resultMap="AovPhaseTeamResult">
        SELECT * FROM aov_phase_team WHERE creator = #{uid} and phase_id = #{phaseId}
    </select>

    <select id="selectById" parameterType="long" resultMap="AovPhaseTeamResult">
        SELECT * FROM aov_phase_team WHERE id = #{id}
    </select>

    <select id="selectByIds" resultMap="AovPhaseTeamResult">
        SELECT * FROM aov_phase_team WHERE id
        in <foreach collection="teamIds" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
    </select>

    <select id="selectTeamCnt" resultMap="AovPhaseTeamResult">
        SELECT count(*) FROM aov_phase_team WHERE phase_id = #{phaseId} and member_cnt >= #{cnt}
    </select>

    <select id="selectInitTeam" resultMap="AovPhaseTeamResult">
        SELECT * FROM aov_phase_team WHERE phase_id = #{phaseId} and member_cnt >= #{cnt} and state = #{state} order by create_time asc
    </select>

    <select id="listTeam" resultMap="AovPhaseTeamResult">
        SELECT * FROM aov_phase_team WHERE phase_id = #{phaseId} and member_cnt &lt; #{cnt} order by create_time desc limit 100
    </select>

    <select id="pageList" resultMap="AovPhaseTeamResult">
        SELECT <include refid="Base_Column_List" />
        FROM aov_phase_team
        WHERE phase_id = #{phaseId}
        order by member_cnt asc
        LIMIT #{offset}, #{pageSize}
    </select>

    <update id="invalidTeam">
        UPDATE aov_phase_team SET state = #{state} WHERE phase_id = #{phaseId} and member_cnt &lt; #{cnt}
    </update>

    <select id="selectValidTeamIds" resultMap="AovPhaseTeamResult">
        select * from aov_phase_team where phase_id = #{phaseId}
        and `state` = ${@com.yy.gameecology.common.consts.aov.AovConst$PhaseTeamState@SUCC}
    </select>
    <select id="selectPhaseTeamBySid" resultMap="AovPhaseTeamResult">
        select * from aov_phase_team where phase_id = #{phaseId} and sid = #{sid} and ssid = #{ssid}
    </select>
    <select id="countTeam" resultType="java.lang.Integer">
        select count(1) from aov_phase_team where phase_id = #{phaseId}
        <if test="state != null">
            and `state` = #{state}
        </if>
        <if test="floorMemberCnt != null">
            and member_cnt &gt;= #{floorMemberCnt}
        </if>
    </select>

    <update id="incrTeamCnt">
        update aov_phase_team
        set member_cnt = member_cnt + 1, team_score = team_score + #{score}
        where id = #{id,jdbcType=BIGINT} and member_cnt &lt; member_limit
    </update>

    <update id="decrTeamCnt">
        update aov_phase_team
        set member_cnt = member_cnt - 1, team_score = team_score - #{score}
        where id = #{id,jdbcType=BIGINT} and member_cnt &gt; 0
    </update>

    <update id="updateState">
        update aov_phase_team set `state` = #{state}
        where id in <foreach collection="ids" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
    </update>

    <update id="acceptDeclaration">
        update aov_phase_team set `audit_state` = #{auditState}, declaration = declaration_audit
        where id = #{id} and audit_state = #{oldState}
    </update>

    <update id="clearAuditDeclaration">
        update aov_phase_team set `audit_state` = #{auditState}, declaration_audit = ''
        where id = #{id} and audit_state = #{oldState}
    </update>

    <update id="updateDeclaration">
        update aov_phase_team set declaration_audit = #{declarationAudit}, audit_state = #{auditState}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="turnNeedApply">
        update aov_phase_team set need_apply = need_apply ^ 1 where id = #{teamId}
    </update>

</mapper>