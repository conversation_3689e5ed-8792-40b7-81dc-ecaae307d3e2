<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.aov.AovAwardAccountExtMapper">
    <insert id="saveAwardAccount" parameterType="com.yy.gameecology.common.db.model.gameecology.aov.AovAwardAccount">
        insert ignore into aov_award_account (account, uid, mobile, create_time) values (#{account}, #{uid}, #{mobile}, #{createTime})
    </insert>

    <select id="selectUidByAccount" resultType="java.lang.Long">
        select `uid` from aov_award_account where `account` = #{account}
    </select>
    <select id="selectAccountByUid" resultType="java.lang.String">
        select `account` from aov_award_account where uid = #{uid}
    </select>
</mapper>