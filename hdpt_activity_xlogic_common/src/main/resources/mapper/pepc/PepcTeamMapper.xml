<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.pepc.PepcTeamMapper">
    <resultMap id="PepcTeamResult" type="com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeam">
        <id property="id" column="id" jdbcType="BIGINT" />
        <result property="sid" column="sid" jdbcType="BIGINT" />
        <result property="ssid" column="ssid" jdbcType="BIGINT" />
        <result property="creator" column="creator" jdbcType="BIGINT" />
        <result property="state" column="state" jdbcType="INTEGER" />
        <result property="needApply" column="need_apply" jdbcType="INTEGER" />
        <result property="teamName" column="team_name" jdbcType="VARCHAR" />
        <result property="teamNameAudit" column="team_name_audit" jdbcType="VARCHAR" />
        <result property="teamNameAuditState" column="team_name_audit_state" jdbcType="INTEGER" />
        <result property="declaration" column="declaration" jdbcType="VARCHAR" />
        <result property="declarationAudit" column="declaration_audit" jdbcType="INTEGER" />
        <result property="auditState" column="audit_state" jdbcType="INTEGER" />
        <result property="memberCnt" column="member_cnt" jdbcType="INTEGER" />
        <result property="memberLimit" column="member_limit" jdbcType="INTEGER" />
        <result property="teamScore" column="team_score" jdbcType="BIGINT" />
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List">
                id,
                act_id,
                sid,
                ssid,
                creator,
                state,
                need_apply,
                team_name,
                team_name_audit,
                team_name_audit_state,
                declaration,
                declaration_audit,
                audit_state,
                member_cnt,
                member_limit,
                team_score,
                create_time
    </sql>

    <insert id="insertAndGetId" useGeneratedKeys="true" keyProperty="id" parameterType="com.yy.gameecology.common.db.model.gameecology.pepc.PepcTeam">
        INSERT INTO pepc_team (sid, ssid, act_id, creator, state, need_apply,team_name,team_name_audit,team_name_audit_state, declaration, declaration_audit, audit_state, member_cnt, member_limit, team_score, create_time)
        VALUES (#{sid}, #{ssid}, #{actId}, #{creator}, #{state}, #{needApply}, #{teamName}, #{teamNameAudit}, #{teamNameAuditState}, #{declaration}, #{declarationAudit}, #{auditState}, #{memberCnt}, #{memberLimit}, #{teamScore}, #{createTime})
    </insert>
    <delete id="deleteTeam">
        delete from pepc_team where id = #{teamId}
                                     and `state` = ${@com.yy.gameecology.common.consts.PepcConst$PhaseTeamState@INIT}
    </delete>

    <select id="selectPepcTeam" parameterType="long" resultMap="PepcTeamResult">
        SELECT * FROM pepc_team  WHERE creator = #{uid} and act_id = #{actId}
    </select>

    <select id="selectById" parameterType="long" resultMap="PepcTeamResult">
        SELECT * FROM pepc_team  WHERE id = #{id}
    </select>

    <select id="selectByIds" resultMap="PepcTeamResult">
        SELECT * FROM pepc_team  WHERE id
        in <foreach collection="teamIds" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
    </select>

    <select id="selectTeamCnt" resultMap="PepcTeamResult">
        SELECT count(*) FROM pepc_team WHERE act_id = #{actId} and member_cnt >= #{cnt}
    </select>

    <select id="selectInitTeam" resultMap="PepcTeamResult">
        SELECT * FROM pepc_team WHERE act_id = #{actId} and member_cnt >= #{cnt} and state = #{state} order by create_time asc
    </select>

    <select id="listTeam" resultMap="PepcTeamResult">
        SELECT * FROM pepc_team WHERE act_id = #{actId} and member_cnt &lt; #{cnt} order by create_time desc limit 100
    </select>

    <select id="pageList" resultMap="PepcTeamResult">
        SELECT <include refid="Base_Column_List" />
        FROM pepc_team
        WHERE act_id = #{actId}
        order by member_cnt asc
        LIMIT #{offset}, #{pageSize}
    </select>

    <update id="invalidTeam">
        UPDATE pepc_team  SET state = #{state} WHERE act_id = #{actId} and member_cnt &lt; #{cnt}
    </update>

    <select id="selectValidTeamIds" resultMap="PepcTeamResult">
        select * from pepc_team  where act_id = #{actId}
                                       and `state` = ${@com.yy.gameecology.common.consts.PepcConst$PhaseTeamState@SUCC}
    </select>
    <select id="selectTeamBySid" resultMap="PepcTeamResult">
        select * from pepc_team  where act_id = #{actId} and sid = #{sid} and ssid = #{ssid} limit 1
    </select>
    <select id="countTeam" resultType="java.lang.Integer">
        select count(1) from pepc_team where act_id = #{actId}
        <if test="state != null">
            and `state` = #{state}
        </if>
        <if test="floorMemberCnt != null">
            and member_cnt &gt;= #{floorMemberCnt}
        </if>
    </select>

<!--    <update id="incrTeamCnt">-->
<!--        update pepc_team-->
<!--        set member_cnt = member_cnt + 1, team_score = team_score + #{score}-->
<!--        where id = #{id,jdbcType=BIGINT} and member_cnt &lt; member_limit-->
<!--    </update>-->

<!--    <update id="decrTeamCnt">-->
<!--        update pepc_team-->
<!--        set member_cnt = member_cnt - 1, team_score = team_score - #{score}-->
<!--        where id = #{id,jdbcType=BIGINT} and member_cnt &gt; 0-->
<!--    </update>-->

    <update id="incrTeamCnt1">
        update pepc_team
        set member_cnt = member_cnt + 1
        where id = #{id,jdbcType=BIGINT} and member_cnt &lt; member_limit
    </update>

    <update id="decrTeamCnt1">
        update pepc_team
        set member_cnt = member_cnt - 1
        where id = #{id,jdbcType=BIGINT} and member_cnt &gt; 0
    </update>

    <update id="updateState">
        update pepc_team  set `state` = #{state}
        where id in <foreach collection="ids" item="teamId" separator="," open="(" close=")">#{teamId}</foreach>
    </update>

    <update id="acceptDeclaration">
        update pepc_team  set `audit_state` = #{auditState}, declaration = declaration_audit
        where id = #{id} and audit_state = #{oldState}
    </update>

    <update id="clearAuditDeclaration">
        update pepc_team set `audit_state` = #{auditState}, declaration_audit = ''
        where id = #{id} and audit_state = #{oldState}
    </update>

    <update id="updateDeclaration">
        update pepc_team set declaration_audit = #{declarationAudit}, audit_state = #{auditState}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="turnNeedApply">
        update pepc_team set need_apply = need_apply ^ 1 where id = #{teamId}
    </update>

</mapper>