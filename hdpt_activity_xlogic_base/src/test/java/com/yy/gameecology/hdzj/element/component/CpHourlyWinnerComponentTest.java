package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.hdzj.bean.CpUid;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * CP小时第一名玩法组件测试类
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class CpHourlyWinnerComponentTest {

    @Test
    public void testComponentAttrScoreRangeLogic() {
        // 测试组件属性中的分数区间逻辑
        CpHourlyWinnerComponentAttr attr = new CpHourlyWinnerComponentAttr();
        
        // 设置分数区间奖励
        AwardAttrConfig award1 = new AwardAttrConfig();
        award1.setAwardName("小额奖励");
        award1.setAwardAmount(50000L);
        
        AwardAttrConfig award2 = new AwardAttrConfig();
        award2.setAwardName("大额奖励");
        award2.setAwardAmount(100000L);
        
        attr.getScoreRangeAwards().put("1000-5000", award1);
        attr.getScoreRangeAwards().put("5001-10000", award2);
        
        // 测试分数区间匹配
        AwardAttrConfig result1 = attr.getAwardByScore(3000L);
        assertNotNull("应该找到对应的奖励配置", result1);
        assertEquals("小额奖励", result1.getAwardName());
        
        AwardAttrConfig result2 = attr.getAwardByScore(8000L);
        assertNotNull("应该找到对应的奖励配置", result2);
        assertEquals("大额奖励", result2.getAwardName());
        
        AwardAttrConfig result3 = attr.getAwardByScore(15000L);
        assertNull("超出范围应该返回null", result3);
    }

    @Test
    public void testGiftPoolLogic() {
        // 测试礼物奖池逻辑
        CpHourlyWinnerComponentAttr attr = new CpHourlyWinnerComponentAttr();
        attr.setGiftPoolCurrentBalance(100000L); // 1000元
        
        // 测试余额充足的情况
        assertTrue("余额充足应该返回true", attr.isGiftPoolSufficient(50000L));
        
        // 测试余额不足的情况
        assertFalse("余额不足应该返回false", attr.isGiftPoolSufficient(150000L));
        
        // 测试扣减余额
        attr.deductGiftPool(30000L);
        assertEquals("扣减后余额应该正确", 70000L, attr.getGiftPoolCurrentBalance());
        
        // 测试扣减超出余额的情况
        attr.deductGiftPool(100000L);
        assertEquals("扣减超出余额时余额不应该变化", 70000L, attr.getGiftPoolCurrentBalance());
    }

    @Test
    public void testComponentId() {
        // 测试组件ID
        CpHourlyWinnerComponent component = new CpHourlyWinnerComponent();
        assertEquals("组件ID应该正确", 5159L, component.getComponentId());
    }

    @Test
    public void testDefaultConfiguration() {
        // 测试默认配置
        CpHourlyWinnerComponentAttr attr = new CpHourlyWinnerComponentAttr();
        
        assertEquals("默认业务ID应该是200", 200L, attr.getBusiId());
        assertEquals("默认礼物奖池总限额应该是40000元", 4000000L, attr.getGiftPoolTotalLimit());
        assertEquals("默认礼物奖池当前余额应该是12960元", 1296000L, attr.getGiftPoolCurrentBalance());
        assertEquals("默认应援口令应该正确", "夏日派对，浪漫加倍", attr.getSupportSlogan());
        assertEquals("默认替代奖励名称应该正确", "夏日飞骏进场秀3天", attr.getFallbackAwardName());
        assertEquals("默认广播模板应该是2", 2, attr.getBroadcastTemplate());
        assertTrue("默认应该启用广播", attr.isEnableBroadcast());
    }

    @Test
    public void testCpMemberSplit() {
        // 测试CP成员解析
        String cpMember = "123456|789012";
        CpUid cpUid = Const.splitCpMember(cpMember);

        assertEquals("用户UID应该正确", 123456L, cpUid.getUserUid());
        assertEquals("主播UID应该正确", 789012L, cpUid.getAnchorUid());
        assertEquals("成员字符串应该正确", cpMember, cpUid.getMember());
    }

    @Test
    public void testAwardConfigValidation() {
        // 测试奖励配置验证
        AwardAttrConfig awardConfig = new AwardAttrConfig();
        awardConfig.setTAwardTskId(100001L);
        awardConfig.setTAwardPkgId(200001L);
        awardConfig.setNum(1);
        awardConfig.setAwardName("测试奖励");
        awardConfig.setAwardAmount(50000L);

        assertNotNull("奖励任务ID不应该为空", awardConfig.getTAwardTskId());
        assertNotNull("奖励包ID不应该为空", awardConfig.getTAwardPkgId());
        assertTrue("奖励数量应该大于0", awardConfig.getNum() > 0);
        assertNotNull("奖励名称不应该为空", awardConfig.getAwardName());
        assertTrue("奖励金额应该大于0", awardConfig.getAwardAmount() > 0);
    }

    @Test
    public void testScoreRangeEdgeCases() {
        // 测试分数区间边界情况
        CpHourlyWinnerComponentAttr attr = new CpHourlyWinnerComponentAttr();

        AwardAttrConfig award1 = new AwardAttrConfig();
        award1.setAwardName("边界奖励1");

        AwardAttrConfig award2 = new AwardAttrConfig();
        award2.setAwardName("边界奖励2");

        attr.getScoreRangeAwards().put("1000-5000", award1);
        attr.getScoreRangeAwards().put("5001-10000", award2);

        // 测试边界值
        AwardAttrConfig result1 = attr.getAwardByScore(1000L); // 最小值
        assertEquals("边界奖励1", result1.getAwardName());

        AwardAttrConfig result2 = attr.getAwardByScore(5000L); // 最大值
        assertEquals("边界奖励1", result2.getAwardName());

        AwardAttrConfig result3 = attr.getAwardByScore(5001L); // 下一区间最小值
        assertEquals("边界奖励2", result3.getAwardName());

        AwardAttrConfig result4 = attr.getAwardByScore(999L); // 小于最小值
        assertNull("超出范围应该返回null", result4);
    }

    @Test
    public void testSupportSloganConfig() {
        // 测试应援口令配置
        CpHourlyWinnerComponentAttr attr = new CpHourlyWinnerComponentAttr();

        // 测试默认配置
        assertEquals("默认应援口令应该正确", "夏日派对，浪漫加倍", attr.getSupportSlogan());
        assertEquals("默认组件索引应该为0", 0L, attr.getWatchwordLotteryComponentIndex());

        // 测试自定义配置
        attr.setSupportSlogan("自定义应援口令");
        attr.setWatchwordLotteryComponentIndex(5L);

        assertEquals("自定义应援口令", attr.getSupportSlogan());
        assertEquals(5L, attr.getWatchwordLotteryComponentIndex());
    }

    @Test
    public void testBroadcastConfig() {
        // 测试广播配置
        CpHourlyWinnerComponentAttr attr = new CpHourlyWinnerComponentAttr();

        // 测试默认配置
        assertTrue("默认应该启用广播", attr.isEnableBroadcast());
        assertEquals("默认广播模板应该是2", 2, attr.getBroadcastTemplate());
        assertEquals("默认广播横幅ID应该为0", 0L, attr.getBroadcastBannerId());
        assertEquals("默认广播横幅类型应该为0", 0L, attr.getBroadcastBannerType());

        // 测试自定义配置
        attr.setEnableBroadcast(false);
        attr.setBroadcastTemplate(3);
        attr.setBroadcastBannerId(123456L);
        attr.setBroadcastBannerType(1L);

        assertFalse("应该禁用广播", attr.isEnableBroadcast());
        assertEquals("广播模板应该是3", 3, attr.getBroadcastTemplate());
        assertEquals("广播横幅ID应该是123456", 123456L, attr.getBroadcastBannerId());
        assertEquals("广播横幅类型应该是1", 1L, attr.getBroadcastBannerType());
    }
}
