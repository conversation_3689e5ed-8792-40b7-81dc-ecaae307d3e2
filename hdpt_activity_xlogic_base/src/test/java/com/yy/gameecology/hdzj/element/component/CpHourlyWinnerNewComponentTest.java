package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerNewComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerNewComponentDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * CP小时第一名组件测试类
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-4.properties"
})
public class CpHourlyWinnerNewComponentTest {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CpHourlyWinnerNewComponent cpHourlyWinnerNewComponent;

    @Autowired
    private CpHourlyWinnerNewComponentDao cpHourlyWinnerNewComponentDao;

    private static final long TEST_ACT_ID = 2025072101L;
    private static final long TEST_CMPT_INX = 1L;

    @Test
    public void testGetComponentId() {
        Long componentId = cpHourlyWinnerNewComponent.getComponentId();
        log.info("Component ID: {}", componentId);
        assert componentId != null && componentId == 5159L;
    }

    @Test
    public void testInsertCpHourlyWinner() {
        Cmpt5159CpHourlyWinner record = new Cmpt5159CpHourlyWinner();
        record.setActId(TEST_ACT_ID);
        record.setCmptUseInx(TEST_CMPT_INX);
        record.setUserUid(50042952L);
        record.setAnchorUid(141772575473674L);
        record.setUserNick("测试用户");
        record.setUserAvatar("http://test.avatar.url");
        record.setAnchorNick("测试主播");
        record.setAnchorAvatar("http://test.anchor.avatar.url");
        record.setCpScore(12345L);
        record.setHourTime("2025072114");
        record.setAwardStatus(0);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        try {
            int result = cpHourlyWinnerNewComponentDao.insertCpHourlyWinner(record);
            log.info("Insert result: {}", result);
            assert result > 0;
        } catch (Exception e) {
            log.warn("Insert test failed (expected in test environment): {}", e.getMessage());
        }
    }

    @Test
    public void testSelectByHourTime() {
        try {
            Cmpt5159CpHourlyWinner record = cpHourlyWinnerNewComponentDao.selectByHourTime(
                    TEST_ACT_ID, TEST_CMPT_INX, "2025072114");
            log.info("Select result: {}", record);
        } catch (Exception e) {
            log.warn("Select test failed (expected in test environment): {}", e.getMessage());
        }
    }

    @Test
    public void testSelectByDate() {
        try {
            List<Cmpt5159CpHourlyWinner> records = cpHourlyWinnerNewComponentDao.selectByDate(
                    TEST_ACT_ID, TEST_CMPT_INX, "20250721");
            log.info("Select by date result size: {}", records.size());
        } catch (Exception e) {
            log.warn("Select by date test failed (expected in test environment): {}", e.getMessage());
        }
    }

    @Test
    public void testComponentAttr() {
        try {
            CpHourlyWinnerNewComponentAttr attr = cpHourlyWinnerNewComponent.getComponentAttr(TEST_ACT_ID, TEST_CMPT_INX);
            if (attr != null) {
                log.info("Component attr loaded successfully");
                log.info("CP Rank ID: {}", attr.getCpRankId());
                log.info("Award Busi ID: {}", attr.getAwardBusiId());
                log.info("Gift Pool Balance: {}", attr.getGiftPoolBalanceInYuan());
            } else {
                log.info("Component attr not found (expected in test environment)");
            }
        } catch (Exception e) {
            log.warn("Component attr test failed (expected in test environment): {}", e.getMessage());
        }
    }

    @Test
    public void testGiftPoolOperations() {
        CpHourlyWinnerNewComponentAttr attr = new CpHourlyWinnerNewComponentAttr();
        attr.setCurrentGiftPoolBalance(1296000L); // 12960.0元

        // 测试余额检查
        assert attr.isGiftPoolSufficient(100000L); // 1000.0元
        assert !attr.isGiftPoolSufficient(2000000L); // 20000.0元

        // 测试扣减
        attr.deductGiftPool(100000L);
        assert attr.getCurrentGiftPoolBalance() == 1196000L;

        // 测试转换
        assert attr.getGiftPoolBalanceInYuan() == 11960.0;

        log.info("Gift pool operations test passed");
    }

    @Test
    public void testScoreRangeAward() {
        CpHourlyWinnerNewComponentAttr attr = new CpHourlyWinnerNewComponentAttr();

        // 创建测试奖励配置
        CpHourlyWinnerNewComponentAttr.ScoreRangeAward award1 = new CpHourlyWinnerNewComponentAttr.ScoreRangeAward();
        award1.setMinScore(0L);
        award1.setMaxScore(1000L);
        award1.setTAwardTskId(10001L);
        award1.setTAwardPkgId(20001L);
        award1.setNum(1);
        award1.setAwardAmount(100000L); // 1000.0元

        CpHourlyWinnerNewComponentAttr.ScoreRangeAward award2 = new CpHourlyWinnerNewComponentAttr.ScoreRangeAward();
        award2.setMinScore(1001L);
        award2.setMaxScore(0L); // 0表示无上限
        award2.setTAwardTskId(10002L);
        award2.setTAwardPkgId(20002L);
        award2.setNum(1);
        award2.setAwardAmount(200000L); // 2000.0元

        attr.getScoreRangeAwards().add(award1);
        attr.getScoreRangeAwards().add(award2);

        // 测试分数区间匹配
        assert attr.getAwardByScore(500L) == award1;
        assert attr.getAwardByScore(1500L) == award2;
        assert attr.getAwardByScore(1000L) == award1;
        assert attr.getAwardByScore(1001L) == award2;

        log.info("Score range award test passed");
    }
}
