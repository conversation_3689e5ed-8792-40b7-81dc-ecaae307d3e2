package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * CP小时第一名组件DAO
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Repository
public class CpHourlyWinnerNewComponentDao {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private GameecologyDao gameecologyDao;

    @Autowired
    private CommonDataDao commonDataDao;

    /**
     * 插入CP小时冠军记录
     */
    @Transactional(rollbackFor = Exception.class)
    public int insertCpHourlyWinner(Cmpt5159CpHourlyWinner record) {
        log.info("insertCpHourlyWinner record:{}", record);
        return gameecologyDao.insert(Cmpt5159CpHourlyWinner.class, record);
    }

    /**
     * 根据小时时间查询冠军记录
     */
    public Cmpt5159CpHourlyWinner selectByHourTime(long actId, long cmptUseInx, String hourTime) {
        Cmpt5159CpHourlyWinner condition = new Cmpt5159CpHourlyWinner();
        condition.setActId(actId);
        condition.setCmptUseInx(cmptUseInx);
        condition.setHourTime(hourTime);

        return gameecologyDao.selectOne(Cmpt5159CpHourlyWinner.class, condition, StringUtils.EMPTY);
    }

    /**
     * 根据日期查询冠军记录列表
     */
    public List<Cmpt5159CpHourlyWinner> selectByDate(long actId, long cmptUseInx, String date) {
        Cmpt5159CpHourlyWinner condition = new Cmpt5159CpHourlyWinner();
        condition.setActId(actId);
        condition.setCmptUseInx(cmptUseInx);

        String afterWhere = " and hour_time like '" + date + "%' order by hour_time desc";
        return gameecologyDao.select(Cmpt5159CpHourlyWinner.class, condition, afterWhere);
    }

    /**
     * 更新奖励发放状态
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateAwardStatus(long actId, long cmptUseInx, String hourTime, int awardStatus, String awardContent) {
        String sql = "UPDATE cmpt_5159_cp_hourly_winner SET award_status = ?, award_content = ?, update_time = NOW() " +
                "WHERE act_id = ? AND cmpt_use_inx = ? AND hour_time = ?";
        return gameecologyDao.update(sql, awardStatus, awardContent, actId, cmptUseInx, hourTime);
    }

    /**
     * 查询最近N条冠军记录
     */
    public List<Cmpt5159CpHourlyWinner> selectRecentWinners(long actId, long cmptUseInx, int limit) {
        Cmpt5159CpHourlyWinner condition = new Cmpt5159CpHourlyWinner();
        condition.setActId(actId);
        condition.setCmptUseInx(cmptUseInx);

        String afterWhere = " order by hour_time desc limit " + limit;
        return gameecologyDao.select(Cmpt5159CpHourlyWinner.class, condition, afterWhere);
    }

    /**
     * 检查是否已处理过指定的榜单结束事件
     */
    public boolean isEventProcessed(long actId, long cmptUseInx, String eventSeq) {
        String checkKey = "cp_hourly_winner_event";
        String checkValue = eventSeq;
        String existingValue = commonDataDao.hashValueGet(actId, 5159L, cmptUseInx, checkKey, checkValue);
        return StringUtils.isNotBlank(existingValue);
    }

    /**
     * 标记事件已处理
     */
    public void markEventProcessed(long actId, long cmptUseInx, String eventSeq) {
        String checkKey = "cp_hourly_winner_event";
        String checkValue = eventSeq;
        commonDataDao.hashValueSet(actId, 5159L, cmptUseInx, checkKey, checkValue, "1");
    }

    /**
     * 更新礼物奖池余额
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateGiftPoolBalance(long actId, long cmptUseInx, long newBalance) {
        String key = "gift_pool_balance";
        commonDataDao.hashValueSet(actId, 5159L, cmptUseInx, key, "balance", String.valueOf(newBalance));
    }

    /**
     * 获取礼物奖池余额
     */
    public long getGiftPoolBalance(long actId, long cmptUseInx, long defaultBalance) {
        String key = "gift_pool_balance";
        String balanceStr = commonDataDao.hashValueGet(actId, 5159L, cmptUseInx, key, "balance");
        if (StringUtils.isNotBlank(balanceStr)) {
            try {
                return Long.parseLong(balanceStr);
            } catch (NumberFormatException e) {
                log.warn("Invalid gift pool balance format: {}", balanceStr);
            }
        }
        return defaultBalance;
    }
}
