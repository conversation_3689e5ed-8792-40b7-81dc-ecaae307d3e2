package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * CP小时第一名组件数据访问层
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Slf4j
@Repository
public class CpHourlyWinnerComponentDao {

    @Autowired
    private GameecologyDao gameecologyDao;

    /**
     * 插入CP小时第一名记录
     */
    public void insertHourlyWinner(Cmpt5159CpHourlyWinner record) {
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());
        gameecologyDao.insert(record);
        log.info("Inserted hourly winner record: {}", record);
    }

    /**
     * 检查事件是否已处理（幂等性检查）
     */
    public boolean isEventProcessed(long actId, long cmptUseInx, String seq) {
        if (StringUtils.isBlank(seq)) {
            return false;
        }
        
        String sql = "SELECT COUNT(1) FROM " + Cmpt5159CpHourlyWinner.TABLE_NAME + 
                     " WHERE act_id = ? AND cmpt_use_inx = ? AND seq = ?";
        
        Integer count = gameecologyDao.queryForObject(sql, Integer.class, actId, cmptUseInx, seq);
        return count != null && count > 0;
    }

    /**
     * 标记事件已处理
     */
    public void markEventProcessed(long actId, long cmptUseInx, String seq) {
        // 这个方法在insertHourlyWinner中已经隐式实现了
        // 因为seq字段的唯一性约束会防止重复插入
        log.info("Event marked as processed: actId={}, cmptUseInx={}, seq={}", actId, cmptUseInx, seq);
    }

    /**
     * 根据日期查询历史小时冠军CP
     */
    public List<Cmpt5159CpHourlyWinner> queryHistoryWinnersByDate(long actId, long cmptUseInx, String dateCode) {
        String sql = "SELECT * FROM " + Cmpt5159CpHourlyWinner.TABLE_NAME + 
                     " WHERE act_id = ? AND cmpt_use_inx = ? AND hour_code LIKE ? ORDER BY hour_code DESC";
        
        String datePattern = dateCode + "%"; // 支持按日期前缀查询
        return gameecologyDao.query(sql, Cmpt5159CpHourlyWinner.ROW_MAPPER, actId, cmptUseInx, datePattern);
    }

    /**
     * 查询指定小时的冠军CP
     */
    public Cmpt5159CpHourlyWinner queryWinnerByHour(long actId, long cmptUseInx, String hourCode) {
        String sql = "SELECT * FROM " + Cmpt5159CpHourlyWinner.TABLE_NAME + 
                     " WHERE act_id = ? AND cmpt_use_inx = ? AND hour_code = ? LIMIT 1";
        
        List<Cmpt5159CpHourlyWinner> results = gameecologyDao.query(sql, Cmpt5159CpHourlyWinner.ROW_MAPPER, 
                actId, cmptUseInx, hourCode);
        
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 更新礼物奖池余额
     */
    public void updateGiftPoolBalance(long actId, long cmptUseInx, long newBalance) {
        String sql = "UPDATE " + Cmpt5159CpHourlyWinner.TABLE_NAME + 
                     " SET gift_pool_remain = ?, update_time = NOW() " +
                     " WHERE act_id = ? AND cmpt_use_inx = ? ORDER BY id DESC LIMIT 1";
        
        int updated = gameecologyDao.update(sql, newBalance, actId, cmptUseInx);
        log.info("Updated gift pool balance: actId={}, cmptUseInx={}, newBalance={}, updated={}", 
                actId, cmptUseInx, newBalance, updated);
    }

    /**
     * 查询最新的礼物奖池余额
     */
    public Long queryLatestGiftPoolBalance(long actId, long cmptUseInx) {
        String sql = "SELECT gift_pool_remain FROM " + Cmpt5159CpHourlyWinner.TABLE_NAME + 
                     " WHERE act_id = ? AND cmpt_use_inx = ? ORDER BY id DESC LIMIT 1";
        
        List<Long> results = gameecologyDao.queryForList(sql, Long.class, actId, cmptUseInx);
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 查询总的获奖记录数
     */
    public int countTotalWinners(long actId, long cmptUseInx) {
        String sql = "SELECT COUNT(1) FROM " + Cmpt5159CpHourlyWinner.TABLE_NAME + 
                     " WHERE act_id = ? AND cmpt_use_inx = ?";
        
        Integer count = gameecologyDao.queryForObject(sql, Integer.class, actId, cmptUseInx);
        return count != null ? count : 0;
    }
}
