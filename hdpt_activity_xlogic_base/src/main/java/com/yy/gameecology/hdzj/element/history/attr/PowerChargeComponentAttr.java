package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.04.22 17:20
 */
public class PowerChargeComponentAttr extends ComponentAttr {
    /**
     * 任务的榜单id
     */
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    /**
     * 任务的阶段id
     */
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    @ComponentAttrField(labelText = "重试次数")
    private int retry;

    @ComponentAttrField(labelText = "用户角色")
    private long playerRole;

    @ComponentAttrField(labelText = "主播角色")
    private long anchorRole;

    @ComponentAttrField(labelText = "用户奖励配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖励数量")
            })
    private Map<Long, Map<Long, Integer>> playerAwardPackageIds = new HashMap<>();

    @ComponentAttrField(labelText = "主播奖励配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "奖池id"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "奖包id"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "奖励数量")
            })
    private Map<Long, Map<Long, Integer>> anchorAwardPackageIds = new HashMap<>();

    @ComponentAttrField(labelText = "CP记录key")
    public static final String LAST_CP = "last_cp";

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public int getRetry() {
        return retry;
    }

    public void setRetry(int retry) {
        this.retry = retry;
    }

    public long getPlayerRole() {
        return playerRole;
    }

    public void setPlayerRole(long playerRole) {
        this.playerRole = playerRole;
    }

    public long getAnchorRole() {
        return anchorRole;
    }

    public void setAnchorRole(long anchorRole) {
        this.anchorRole = anchorRole;
    }

    public Map<Long, Map<Long, Integer>> getPlayerAwardPackageIds() {
        return playerAwardPackageIds;
    }

    public void setPlayerAwardPackageIds(Map<Long, Map<Long, Integer>> playerAwardPackageIds) {
        this.playerAwardPackageIds = playerAwardPackageIds;
    }

    public Map<Long, Map<Long, Integer>> getAnchorAwardPackageIds() {
        return anchorAwardPackageIds;
    }

    public void setAnchorAwardPackageIds(Map<Long, Map<Long, Integer>> anchorAwardPackageIds) {
        this.anchorAwardPackageIds = anchorAwardPackageIds;
    }
}
