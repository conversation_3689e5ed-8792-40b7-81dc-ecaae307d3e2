package com.yy.gameecology.hdzj.bean.daytask;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2024-09-23 15:26
 **/
@Data
public class CurDayTask {
    /**
     * 当前做到哪一天任务
     * 如果已完成任务，并且时间跨越了当天，则这里会+1
     */
    private int dayShowIndex;

    /**
     * 已完成的任务天数
     */
    private int signedDay;

    /**
     * 最近完成任务的时间
     */
    private String completeDayCode;

    /**
     * 当日子任务
     */
    private List<CurDayTaskItem> curTaskItem = Lists.newArrayList();

    public boolean allComplete(long lastDayIndex) {
        return signedDay == lastDayIndex && signedDay > 0;
    }
}
