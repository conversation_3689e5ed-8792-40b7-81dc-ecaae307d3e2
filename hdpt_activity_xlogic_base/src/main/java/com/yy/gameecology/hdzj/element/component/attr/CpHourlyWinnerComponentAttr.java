package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.common.consts.Constant;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件属性配置
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Data
public class CpHourlyWinnerComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "业务ID", dropDownSourceBeanClass = BizSource.class)
    private long busiId = 200;

    @ComponentAttrField(labelText = "CP榜单ID", remark = "监听的CP榜单ID")
    private long cpRankId;

    @ComponentAttrField(labelText = "分数区间奖励配置", remark = "根据第一名分数区间发放不同奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "分数区间", remark = "格式：min-max，如：1000-5000"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<String, AwardAttrConfig> scoreRangeAwards = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "全服礼物奖池总限额", remark = "单位：分（1元=100分）")
    private long giftPoolTotalLimit = 4000000L; // 40000元

    @ComponentAttrField(labelText = "全服礼物奖池当前余额", remark = "单位：分（1元=100分）")
    private long giftPoolCurrentBalance = 1296000L; // 12960元

    @ComponentAttrField(labelText = "奖池不足时的替代奖励", remark = "当礼物奖池余额不足时发放的奖励")
    private AwardAttrConfig fallbackAward;

    @ComponentAttrField(labelText = "替代奖励名称", remark = "奖池不足时发放的奖励名称")
    private String fallbackAwardName = "夏日飞骏进场秀3天";

    @ComponentAttrField(labelText = "应援口令文案", remark = "发送到房间的应援口令")
    private String supportSlogan = "夏日派对，浪漫加倍";

    @ComponentAttrField(labelText = "应援口令抽奖组件索引", remark = "调用ChannelWatchwordLotteryComponent的组件索引")
    private long watchwordLotteryComponentIndex;

    @ComponentAttrField(labelText = "广播模板类型", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broadcastTemplate = 2;

    @ComponentAttrField(labelText = "是否启用广播", remark = "是否广播第一名CP信息")
    private boolean enableBroadcast = true;

    @ComponentAttrField(labelText = "广播横幅ID")
    private long broadcastBannerId;

    @ComponentAttrField(labelText = "广播横幅类型")
    private long broadcastBannerType;

    /**
     * 根据分数获取对应的奖励配置
     */
    public AwardAttrConfig getAwardByScore(long score) {
        for (Map.Entry<String, AwardAttrConfig> entry : scoreRangeAwards.entrySet()) {
            String range = entry.getKey();
            if (isScoreInRange(score, range)) {
                return entry.getValue();
            }
        }
        return null;
    }

    /**
     * 检查分数是否在指定区间内
     */
    private boolean isScoreInRange(long score, String range) {
        try {
            String[] parts = range.split("-");
            if (parts.length != 2) {
                return false;
            }
            long min = Long.parseLong(parts[0].trim());
            long max = Long.parseLong(parts[1].trim());
            return score >= min && score <= max;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查礼物奖池余额是否足够
     */
    public boolean isGiftPoolSufficient(long requiredAmount) {
        return giftPoolCurrentBalance >= requiredAmount;
    }

    /**
     * 扣减礼物奖池余额
     */
    public void deductGiftPool(long amount) {
        if (giftPoolCurrentBalance >= amount) {
            giftPoolCurrentBalance -= amount;
        }
    }
}
