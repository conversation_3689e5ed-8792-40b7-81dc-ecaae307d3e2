package com.yy.gameecology.hdzj.bean.daytask;

import com.google.common.collect.Lists;
import com.yy.gameecology.common.db.model.gameecology.Cmpt1017DayTaskItemState;
import com.yy.gameecology.common.db.model.gameecology.Cmpt1017DayTaskState;
import lombok.Data;

import java.util.List;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-03-13 14:10
 **/
@Data
public class DayTaskState {
    private Cmpt1017DayTaskState dayTaskState;
    private List<Cmpt1017DayTaskItemState> itemStates = Lists.newArrayList();
}
