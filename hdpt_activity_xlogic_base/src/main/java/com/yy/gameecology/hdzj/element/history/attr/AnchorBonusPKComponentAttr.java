package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.08.30 15:29
 */
public class AnchorBonusPKComponentAttr extends ComponentAttr {
    /**
     * 每阶段分到的钱
     */
    @ComponentAttrField(labelText = "每阶段分到的钱",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "阶段id"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "奖励数量")
            })
    private Map<Long, Long> phaseAwardMap;
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;

    /**
     * 初始阶段,获取pk名单
     */
    @ComponentAttrField(labelText = "初始阶段", remark = "用于获取pk名单")
    private long pkBasePhase;

    /**
     * 初始晋级人数
     */
    @ComponentAttrField(labelText = "初始晋级人数")
    private long pkBaseCount;

    public Map<Long, Long> getPhaseAwardMap() {
        return phaseAwardMap;
    }

    public void setPhaseAwardMap(Map<Long, Long> phaseAwardMap) {
        this.phaseAwardMap = phaseAwardMap;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPkBasePhase() {
        return pkBasePhase;
    }

    public void setPkBasePhase(long pkBasePhase) {
        this.pkBasePhase = pkBasePhase;
    }

    public long getPkBaseCount() {
        return pkBaseCount;
    }

    public void setPkBaseCount(long pkBaseCount) {
        this.pkBaseCount = pkBaseCount;
    }
}
