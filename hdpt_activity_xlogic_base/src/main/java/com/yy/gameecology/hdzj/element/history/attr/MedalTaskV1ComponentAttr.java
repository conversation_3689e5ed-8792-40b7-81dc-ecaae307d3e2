package com.yy.gameecology.hdzj.element.history.attr;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;

import java.util.Map;

/**
 * @Author: CXZ
 * @Desciption: 勋章任务玩法组件属性
 * @Date: 2021/4/15 16:39
 * @Modified:
 */
public class MedalTaskV1ComponentAttr extends ComponentAttr {


    //--------------------------      勋章任务配置    -----------------------//
    /**
     *
     */
    @ComponentAttrField(labelText = "业务id列表", remark = "多个用逗号分隔,业务枚举：200:游戏生态,400:游戏宝贝,500:交友,600:约战,900:陪玩",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Long[] busiIds;
    /**
     * 勋章任务的榜单id
     */
    @ComponentAttrField(labelText = "榜单id")
    private long rankId;
    /**
     * 勋章任务的阶段id
     */
    @ComponentAttrField(labelText = "阶段id")
    private long phaseId;

    /**
     * 计分的礼物id
     */
    @ComponentAttrField(labelText = "计分的礼物id", remark = "多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = String.class)})
    private String[] giftItems;
    /**
     * 勋章等级对应需要的分数
     */
    @ComponentAttrField(labelText = "勋章等级对应的分数",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "勋章等级"),
                    @SubField(fieldName = Constant.VALUE, type = Long.class, labelText = "分数")
            })
    private Map<Integer, Long> levelScoreMap;


    //--------------------------      勋章其他来源    -----------------------//
    /**
     * 其他来源发放的勋章<taskId-packageId,level>
     */
    @ComponentAttrField(labelText = "奖包来源",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "奖池+奖包"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "勋章等级")
            })
    private Map<String, Integer> taskPackageLevelMap = Maps.newHashMap();

    /**
     * 素材--大图广播，小图给pc查询接口（交友）
     */
    @ComponentAttrField(labelText = "勋章素材",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "勋章等级"),
                    @SubField(fieldName = Constant.VALUE, type = MedalMaterial.class)
            })
    Map<Integer, MedalMaterial> medalMaterialMap;

    /**
     * 勋章升级广播配置，4=本业务模板内广播，5=活动业务广播
     */
    @ComponentAttrField(labelText = "勋章升级广播配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Integer.class, labelText = "勋章等级"),
                    @SubField(fieldName = Constant.VALUE, type = Integer.class, labelText = "广播范围", remark = "4=本业务模板内广播，5=活动业务广播")
            })
    Map<Integer, Integer> levelBroTypeMap = Maps.newHashMap();


    public Long[] getBusiIds() {
        return busiIds;
    }

    public void setBusiIds(Long[] busiIds) {
        this.busiIds = busiIds;
    }

    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public String[] getGiftItems() {
        return giftItems;
    }

    public void setGiftItems(String[] giftItems) {
        this.giftItems = giftItems;
    }

    public Map<Integer, Long> getLevelScoreMap() {
        return levelScoreMap;
    }

    public void setLevelScoreMap(Map<Integer, Long> levelScoreMap) {
        this.levelScoreMap = levelScoreMap;
    }


    public Map<Integer, MedalMaterial> getMedalMaterialMap() {
        return medalMaterialMap;
    }

    public void setMedalMaterialMap(Map<Integer, MedalMaterial> medalMaterialMap) {
        this.medalMaterialMap = medalMaterialMap;
    }

    public Map<String, Integer> getTaskPackageLevelMap() {
        return taskPackageLevelMap;
    }

    public void setTaskPackageLevelMap(Map<String, Integer> taskPackageLevelMap) {
        this.taskPackageLevelMap = taskPackageLevelMap;
    }

    public Map<Integer, Integer> getLevelBroTypeMap() {
        return levelBroTypeMap;
    }

    public void setLevelBroTypeMap(Map<Integer, Integer> levelBroTypeMap) {
        this.levelBroTypeMap = levelBroTypeMap;
    }

    public static class MedalMaterial {
        @ComponentAttrField(labelText = "勋章名称")
        private String name;
        /**
         * 小图url
         **/
        @ComponentAttrField(labelText = "勋章小图url")
        private String smallIcon;
        /**
         * 大图url
         **/
        @ComponentAttrField(labelText = "勋章大图url")
        private String largeIcon;

        // 拆成小图url和大图url
        // Map<String, String> imageMap;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

//        public Map<String, String> getImageMap() {
//            return imageMap;
//        }
//
//        public void setImageMap(Map<String, String> imageMap) {
//            this.imageMap = imageMap;
//        }


        public String getSmallIcon() {
            return smallIcon;
        }

        public void setSmallIcon(String smallIcon) {
            this.smallIcon = smallIcon;
        }

        public String getLargeIcon() {
            return largeIcon;
        }

        public void setLargeIcon(String largeIcon) {
            this.largeIcon = largeIcon;
        }
    }

    public static void main(String[] args) {
        MedalMaterial medalMaterial = new MedalMaterial();
        medalMaterial.setName("25");
        //medalMaterial.setImageMap(ImmutableMap.of("66x77", "ur", "66x87", "ur"));
        String a = JSON.toJSONString(medalMaterial);
        System.out.println();
    }
}
