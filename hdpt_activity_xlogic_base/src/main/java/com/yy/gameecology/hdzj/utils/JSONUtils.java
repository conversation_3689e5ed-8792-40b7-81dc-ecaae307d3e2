package com.yy.gameecology.hdzj.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.protobuf.Message;
import com.googlecode.protobuf.format.JsonFormat;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * jackson support jsr310 and jdk8
 *
 * <AUTHOR>
 * https://github.com/FasterXML/jackson-modules-java8
 * @since 2021/6/30
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
@Slf4j
public class JSONUtils {

    public final static String ERROR_MESSAGE = "Exception thrown while parsing ObjectMapper.";

    private final static ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        OBJECT_MAPPER.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE);
        OBJECT_MAPPER.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        OBJECT_MAPPER.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES, false);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        OBJECT_MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        OBJECT_MAPPER.configure(JsonGenerator.Feature.IGNORE_UNKNOWN, true);
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //jsr310
        OBJECT_MAPPER.registerModule(new JavaTimeModule());
    }


    /**
     * 将String类型的json数据解析为Java对象
     *
     * @param content 需要解析的内容
     * @param type    需要解析的类型Class
     * @param <T>     需要解析的类型
     * @return 一个Java对象由 {@param type}指定，如果解析异常则返回null.
     */
    public static <T> T parseObject(String content, Class<T> type) {
        return parseObject(content, type, true);
    }

    /**
     * 从输入流中读取数据解析为Java对象
     *
     * @param content          需要解析的内容
     * @param type             需要解析的类型Class
     * @param swallowException 是否吞掉异常，如果是则不抛出异常，否则异常往上抛出
     * @param <T>              需要解析的类型
     * @return 一个Java对象由{@param type}指定，当解析产生异常而{@param swallowException}为false的时候则会将异常抛出，
     * 客户方需要捕获异常，为true则会吞掉异常，返回null
     */
    public static <T> T parseObject(String content, Class<T> type, boolean swallowException) {
        try {
            return OBJECT_MAPPER.readValue(content, type);
        } catch (Exception e) {
            swallowException(swallowException, e);
            log.error(ERROR_MESSAGE, e);
        }
        return null;
    }

    public static <T> T parseObject(String content, TypeReference<T> valueTypeRef) {
        return parseObject(content, valueTypeRef, true);
    }

    public static <T> T parseObject(String content, TypeReference<T> valueTypeRef, boolean swallowException) {
        try {
            return OBJECT_MAPPER.readValue(content, valueTypeRef);
        } catch (Exception e) {
            swallowException(swallowException, e);
            log.error(ERROR_MESSAGE, e);
        }
        return null;
    }


    /**
     * 将对象序列化为json字符串
     *
     * @param object 需要序列化的对象
     * @return 一个json规范的String，如果出现异常则返回null.
     */
    public static String toJsonString(Object object) {
        return toJsonString(object, true);
    }

    /**
     * 将对象序列化为json字符串
     *
     * @param object           需要序列化的对象
     * @param swallowException 是否吞掉异常，如果是则不抛出异常，否则异常往上抛出
     * @return 一个json规范的String，当解析产生异常而{@param swallowException}为false的时候则会将异常抛出，
     * 客户方需要捕获异常，为true则会吞掉异常，返回null
     */
    public static String toJsonString(Object object, boolean swallowException) {
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (Exception e) {
            swallowException(swallowException, e);
            log.error(ERROR_MESSAGE, e);
        }
        return StringUtils.EMPTY;
    }

    public static <T> ByteBuffer toByteBuffer(T content){
        if (content instanceof String) {
            return ByteBuffer.wrap(((String) content).getBytes(StandardCharsets.UTF_8));
        }
        return ByteBuffer.wrap(JSONUtils.toJsonString(content).getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 异常处理，是否吞掉异常，使用参数控制
     *
     * @param swallowException 是否吞掉异常
     * @param e                Json相关编解码异常
     */
    private static void swallowException(boolean swallowException, Exception e) {
        if (!swallowException) {
            throw new JSONException(e);
        }
    }

    public String listToJson(List<? extends Message> messages) {
        List<JSONObject> jsonList = new ArrayList<>();
        for (Message message : messages) {
            // 将 Protobuf 消息转换为 JSON 字符串
            String jsonStr = JsonFormat.printToString(message);
            // 将 JSON 字符串解析为 JSONObject
            JSONObject jsonObject = JSON.parseObject(jsonStr);
            jsonList.add(jsonObject);
        }
        // 使用 FastJSON 将列表转换为最终的 JSON 字符串
        return JSON.toJSONString(jsonList);
    }

}
