package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerComponentDao;
import com.yy.protocol.pb.GameecologyActivity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * CP小时第一名玩法组件
 * 实现每小时CP榜单第一名的奖励发放、应援口令、广播等功能
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Slf4j
@Component
@RestController
@RequestMapping("/cpHourlyWinner")
public class CpHourlyWinnerComponent extends BaseActComponent<CpHourlyWinnerComponentAttr> {

    @Autowired
    private CpHourlyWinnerComponentDao cpHourlyWinnerComponentDao;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    // TODO: 注入ChannelWatchwordLotteryComponent，需要确认正确的注入方式
    // @Autowired
    // private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Override
    public long getComponentId() {
        return ComponentId.CP_HOURLY_WINNER;
    }

    /**
     * 监听榜单结束事件，处理每小时CP榜单第一名结算
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("onRankingTimeEnd start event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        // 检查是否是我们关心的榜单
        if (event.getRankId() != attr.getCpRankId()) {
            return;
        }

        // 防重检查
        String eventSeq = event.getSeq();
        if (cpHourlyWinnerComponentDao.isEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq)) {
            log.warn("Event already processed, skip. eventSeq:{}", eventSeq);
            return;
        }

        try {
            // 处理小时榜单结算
            processHourlyRankSettle(event, attr);
            
            // 标记事件已处理
            cpHourlyWinnerComponentDao.markEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq);
            
            log.info("onRankingTimeEnd completed successfully");
        } catch (Exception e) {
            log.error("onRankingTimeEnd failed", e);
            throw e; // 重新抛出异常以触发重试
        }
    }

    /**
     * 处理小时榜单结算逻辑
     */
    private void processHourlyRankSettle(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        // TODO: 实现榜单结算逻辑
        // 1. 获取榜单第一名信息
        // 2. 记录第一名CP信息到数据库
        // 3. 根据分数区间发放奖励
        // 4. 处理礼物奖池逻辑
        // 5. 发送应援口令到房间
        // 6. 广播第一名CP信息
        
        log.info("TODO: Implement processHourlyRankSettle logic");
        
        // 示例：创建记录（实际实现时需要获取真实的榜单数据）
        Cmpt5159CpHourlyWinner record = new Cmpt5159CpHourlyWinner();
        record.setActId(attr.getActId());
        record.setCmptUseInx(attr.getCmptUseInx().intValue());
        record.setSeq(event.getSeq());
        record.setHourCode(DateUtil.format(new Date(), "yyyyMMddHH"));
        // TODO: 设置其他字段
        
        // cpHourlyWinnerComponentDao.insertHourlyWinner(record);
    }

    /**
     * HTTP接口1：查询历史小时冠军CP
     */
    @RequestMapping("/queryHistoryWinners")
    public GameecologyActivity.Response queryHistoryWinners(
            @RequestParam long actId,
            @RequestParam long cmptUseInx,
            @RequestParam String dateCode) {
        
        log.info("queryHistoryWinners start: actId={}, cmptUseInx={}, dateCode={}", actId, cmptUseInx, dateCode);
        
        try {
            // TODO: 实现查询历史小时冠军CP逻辑
            List<Cmpt5159CpHourlyWinner> winners = cpHourlyWinnerComponentDao.queryHistoryWinnersByDate(actId, cmptUseInx, dateCode);
            
            // TODO: 转换为前端需要的格式
            HistoryWinnersResponse response = new HistoryWinnersResponse();
            // 设置响应数据
            
            log.info("queryHistoryWinners completed: found {} winners", winners.size());
            
            return GameecologyActivity.Response.newBuilder()
                    .setCode(0)
                    .setMsg("success")
                    .setData(JSON.toJSONString(response))
                    .build();
                    
        } catch (Exception e) {
            log.error("queryHistoryWinners failed", e);
            return GameecologyActivity.Response.newBuilder()
                    .setCode(-1)
                    .setMsg("查询失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * HTTP接口2：查询全服礼物奖池余额
     */
    @RequestMapping("/queryGiftPoolBalance")
    public GameecologyActivity.Response queryGiftPoolBalance(
            @RequestParam long actId,
            @RequestParam long cmptUseInx) {
        
        log.info("queryGiftPoolBalance start: actId={}, cmptUseInx={}", actId, cmptUseInx);
        
        try {
            // TODO: 实现查询礼物奖池余额逻辑
            Long balance = cpHourlyWinnerComponentDao.queryLatestGiftPoolBalance(actId, cmptUseInx);
            if (balance == null) {
                // 如果没有记录，返回配置中的初始余额
                CpHourlyWinnerComponentAttr attr = getComponentAttr(actId, cmptUseInx);
                balance = attr.getGiftPoolCurrentBalance();
            }
            
            GiftPoolBalanceResponse response = new GiftPoolBalanceResponse();
            response.setBalance(balance);
            response.setBalanceYuan(balance / 100.0); // 转换为元
            
            log.info("queryGiftPoolBalance completed: balance={}", balance);
            
            return GameecologyActivity.Response.newBuilder()
                    .setCode(0)
                    .setMsg("success")
                    .setData(JSON.toJSONString(response))
                    .build();
                    
        } catch (Exception e) {
            log.error("queryGiftPoolBalance failed", e);
            return GameecologyActivity.Response.newBuilder()
                    .setCode(-1)
                    .setMsg("查询失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 历史冠军查询响应对象
     */
    @Data
    public static class HistoryWinnersResponse {
        private List<WinnerInfo> winners;
        
        @Data
        public static class WinnerInfo {
            private String userNick;
            private String userAvatar;
            private String anchorNick;
            private String anchorAvatar;
            private String hourCode;
            private Long score;
        }
    }

    /**
     * 礼物奖池余额查询响应对象
     */
    @Data
    public static class GiftPoolBalanceResponse {
        private Long balance; // 余额（分）
        private Double balanceYuan; // 余额（元）
    }
}
