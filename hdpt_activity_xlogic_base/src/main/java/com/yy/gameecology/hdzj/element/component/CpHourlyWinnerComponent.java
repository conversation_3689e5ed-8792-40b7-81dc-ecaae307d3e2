package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.OnlineChannelService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.ChannelInfoVo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MD5SHAUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerComponentDao;
import com.yy.java.webdb.UserInfoWithNickExt;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件
 * 实现每小时CP榜单第一名的奖励发放、应援口令、广播等功能
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Slf4j
@Component
@RestController
@RequestMapping("/cpHourlyWinner")
public class CpHourlyWinnerComponent extends BaseActComponent<CpHourlyWinnerComponentAttr> {

    @Autowired
    private CpHourlyWinnerComponentDao cpHourlyWinnerComponentDao;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private OnlineChannelService onlineChannelService;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Override
    public long getComponentId() {
        return ComponentId.CP_HOURLY_WINNER;
    }

    /**
     * 监听榜单结束事件，处理每小时CP榜单第一名结算
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("onRankingTimeEnd start event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        // 检查是否是我们关心的榜单
        if (event.getRankId() != attr.getCpRankId()) {
            return;
        }

        // 防重检查
        String eventSeq = event.getSeq();
        if (cpHourlyWinnerComponentDao.isEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq)) {
            log.warn("Event already processed, skip. eventSeq:{}", eventSeq);
            return;
        }

        try {
            // 处理小时榜单结算
            processHourlyRankSettle(event, attr);
            
            // 标记事件已处理
            cpHourlyWinnerComponentDao.markEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq);
            
            log.info("onRankingTimeEnd completed successfully");
        } catch (Exception e) {
            log.error("onRankingTimeEnd failed", e);
            throw e; // 重新抛出异常以触发重试
        }
    }

    /**
     * 处理小时榜单结算逻辑
     */
    private void processHourlyRankSettle(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("processHourlyRankSettle start, event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        try {
            // 1. 获取榜单第一名信息
            Rank firstRank = getFirstRankFromEvent(event, attr);
            if (firstRank == null) {
                log.warn("No first rank found, skip processing");
                return;
            }

            // 2. 解析CP成员信息
            CpUid cpUid = Const.splitCpMember(firstRank.getMember());
            log.info("First rank CP: userUid={}, anchorUid={}, score={}",
                    cpUid.getUserUid(), cpUid.getAnchorUid(), firstRank.getScore());

            // 3. 获取用户信息
            UserInfoWithNickExt userInfo = userInfoService.getUserInfoWithNickExt(cpUid.getUserUid());
            UserInfoWithNickExt anchorInfo = userInfoService.getUserInfoWithNickExt(cpUid.getAnchorUid());

            if (userInfo == null || anchorInfo == null) {
                log.warn("Failed to get user info: userInfo={}, anchorInfo={}", userInfo, anchorInfo);
                return;
            }

            // 4. 创建数据库记录
            Cmpt5159CpHourlyWinner record = createHourlyWinnerRecord(event, attr, firstRank, cpUid, userInfo, anchorInfo);

            // 5. 根据分数区间发放奖励
            processAwardByScore(event, attr, firstRank.getScore(), cpUid, record);

            // 6. 发送应援口令到房间
            sendSupportSloganToRoom(attr, cpUid);

            // 7. 广播第一名CP信息
            broadcastWinnerInfo(attr, userInfo, anchorInfo, firstRank.getScore());

            // 8. 保存记录到数据库
            cpHourlyWinnerComponentDao.insertHourlyWinner(record);

            log.info("processHourlyRankSettle completed successfully");

        } catch (Exception e) {
            log.error("processHourlyRankSettle failed", e);
            throw e;
        }
    }

    /**
     * 从事件中获取榜单第一名信息
     */
    private Rank getFirstRankFromEvent(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        try {
            // 计算小时时间码
            Date endTime = DateUtil.getDate(event.getEndTime());
            String hourCode = DateUtil.format(endTime, "yyyyMMddHH");

            // 查询榜单第一名
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(
                    attr.getActId(),
                    attr.getCpRankId(),
                    event.getPhaseId(),
                    hourCode,
                    1,
                    Maps.newHashMap()
            );

            if (CollectionUtils.isEmpty(ranks)) {
                log.warn("No ranking data found for hourCode:{}", hourCode);
                return null;
            }

            Rank firstRank = ranks.get(0);
            if (firstRank.getScore() <= 0) {
                log.warn("First rank score is zero, skip. hourCode:{}", hourCode);
                return null;
            }

            return firstRank;

        } catch (Exception e) {
            log.error("Failed to get first rank from event", e);
            return null;
        }
    }

    /**
     * 创建小时冠军记录
     */
    private Cmpt5159CpHourlyWinner createHourlyWinnerRecord(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr,
                                                            Rank firstRank, CpUid cpUid,
                                                            UserInfoWithNickExt userInfo, UserInfoWithNickExt anchorInfo) {
        Date endTime = DateUtil.getDate(event.getEndTime());
        String hourCode = DateUtil.format(endTime, "yyyyMMddHH");

        Cmpt5159CpHourlyWinner record = new Cmpt5159CpHourlyWinner();
        record.setActId(attr.getActId());
        record.setCmptUseInx(attr.getCmptUseInx().intValue());
        record.setSeq(event.getSeq());
        record.setHourCode(hourCode);
        record.setUserUid(cpUid.getUserUid());
        record.setAnchorUid(cpUid.getAnchorUid());
        record.setUserNick(userInfo.getNick());
        record.setAnchorNick(anchorInfo.getNick());
        record.setUserAvatar(userInfo.getLogo());
        record.setAnchorAvatar(anchorInfo.getLogo());
        record.setScore(firstRank.getScore());

        // 获取房间信息
        try {
            ChannelInfoVo channelInfo = getAnchorChannelInfo(cpUid.getAnchorUid());
            if (channelInfo != null) {
                record.setSid(channelInfo.getSid());
                record.setSsid(channelInfo.getSsid());
                log.info("Set room info for record: anchorUid={}, sid={}, ssid={}",
                        cpUid.getAnchorUid(), channelInfo.getSid(), channelInfo.getSsid());
            } else {
                record.setSid(0L);
                record.setSsid(0L);
                log.warn("No room info found for anchor:{}", cpUid.getAnchorUid());
            }
        } catch (Exception e) {
            log.warn("Failed to get room info for anchor:{}", cpUid.getAnchorUid(), e);
            record.setSid(0L);
            record.setSsid(0L);
        }

        return record;
    }

    /**
     * 根据分数区间发放奖励
     */
    private void processAwardByScore(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr,
                                   long score, CpUid cpUid, Cmpt5159CpHourlyWinner record) {
        try {
            // 根据分数获取奖励配置
            AwardAttrConfig awardConfig = attr.getAwardByScore(score);
            if (awardConfig == null) {
                log.warn("No award config found for score:{}", score);
                return;
            }

            log.info("Found award config for score:{}, awardConfig:{}", score, JSON.toJSONString(awardConfig));

            // 检查礼物奖池余额
            long awardAmount = awardConfig.getAwardAmount();
            boolean isGiftPoolSufficient = attr.isGiftPoolSufficient(awardAmount);

            if (isGiftPoolSufficient) {
                // 奖池余额充足，发放正常奖励
                sendNormalAward(event, attr, awardConfig, cpUid, record);
                // 扣减奖池余额
                attr.deductGiftPool(awardAmount);
                record.setGiftPoolRemain(attr.getGiftPoolCurrentBalance());
                record.setAwardAmount(awardAmount);
                record.setAwardType(1); // 礼物奖励
            } else {
                // 奖池余额不足，发放替代奖励
                sendFallbackAward(event, attr, cpUid, record);
                record.setGiftPoolRemain(attr.getGiftPoolCurrentBalance());
                record.setAwardAmount(0L);
                record.setAwardType(2); // 进场秀奖励
            }

        } catch (Exception e) {
            log.error("Failed to process award by score", e);
            throw e;
        }
    }

    /**
     * 发放正常奖励
     */
    private void sendNormalAward(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr,
                               AwardAttrConfig awardConfig, CpUid cpUid, Cmpt5159CpHourlyWinner record) {
        try {
            String time = DateUtil.getNowYyyyMMddHHmmss();
            String seq = event.getSeq() + "_award_" + cpUid.getUserUid() + "_" + cpUid.getAnchorUid();
            String seqMd5 = MD5SHAUtil.getMD5(seq);

            // 构建奖励包信息
            Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
            Map<Long, Integer> packageIdAmount = Maps.newHashMap();

            if (awardConfig.getTAwardPkgId() != null && awardConfig.getTAwardPkgId() > 0) {
                packageIdAmount.put(awardConfig.getTAwardPkgId(), awardConfig.getNum());
                taskPackageIds.put(awardConfig.getTAwardTskId(), packageIdAmount);
            }

            // 分别给用户和主播发奖
            if (!taskPackageIds.isEmpty()) {
                // 给用户发奖
                hdztAwardServiceClient.doBatchWelfare(seqMd5 + "_user", cpUid.getUserUid(),
                        taskPackageIds, time, 3, Maps.newHashMap());

                // 给主播发奖
                hdztAwardServiceClient.doBatchWelfare(seqMd5 + "_anchor", cpUid.getAnchorUid(),
                        taskPackageIds, time, 3, Maps.newHashMap());

                log.info("Normal award sent successfully: userUid={}, anchorUid={}, awardConfig={}",
                        cpUid.getUserUid(), cpUid.getAnchorUid(), JSON.toJSONString(awardConfig));
            }

        } catch (Exception e) {
            log.error("Failed to send normal award", e);
            throw e;
        }
    }

    /**
     * 发放替代奖励
     */
    private void sendFallbackAward(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr,
                                 CpUid cpUid, Cmpt5159CpHourlyWinner record) {
        try {
            AwardAttrConfig fallbackAward = attr.getFallbackAward();
            if (fallbackAward == null) {
                log.warn("No fallback award config found");
                return;
            }

            String time = DateUtil.getNowYyyyMMddHHmmss();
            String seq = event.getSeq() + "_fallback_" + cpUid.getUserUid() + "_" + cpUid.getAnchorUid();
            String seqMd5 = MD5SHAUtil.getMD5(seq);

            // 构建替代奖励包信息
            Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
            Map<Long, Integer> packageIdAmount = Maps.newHashMap();

            if (fallbackAward.getTAwardPkgId() != null && fallbackAward.getTAwardPkgId() > 0) {
                packageIdAmount.put(fallbackAward.getTAwardPkgId(), fallbackAward.getNum());
                taskPackageIds.put(fallbackAward.getTAwardTskId(), packageIdAmount);
            }

            // 分别给用户和主播发奖
            if (!taskPackageIds.isEmpty()) {
                // 给用户发奖
                hdztAwardServiceClient.doBatchWelfare(seqMd5 + "_user", cpUid.getUserUid(),
                        taskPackageIds, time, 3, Maps.newHashMap());

                // 给主播发奖
                hdztAwardServiceClient.doBatchWelfare(seqMd5 + "_anchor", cpUid.getAnchorUid(),
                        taskPackageIds, time, 3, Maps.newHashMap());

                log.info("Fallback award sent successfully: userUid={}, anchorUid={}, fallbackAward={}",
                        cpUid.getUserUid(), cpUid.getAnchorUid(), JSON.toJSONString(fallbackAward));
            }

        } catch (Exception e) {
            log.error("Failed to send fallback award", e);
            throw e;
        }
    }

    /**
     * 发送应援口令到房间
     */
    private void sendSupportSloganToRoom(CpHourlyWinnerComponentAttr attr, CpUid cpUid) {
        try {
            String supportSlogan = attr.getSupportSlogan();
            long componentIndex = attr.getWatchwordLotteryComponentIndex();

            if (StringUtils.isBlank(supportSlogan) || componentIndex <= 0) {
                log.warn("Invalid support slogan config: slogan={}, componentIndex={}", supportSlogan, componentIndex);
                return;
            }

            // 1. 获取主播所在房间信息
            ChannelInfoVo channelInfo = getAnchorChannelInfo(cpUid.getAnchorUid());
            if (channelInfo == null || channelInfo.getSid() <= 0 || channelInfo.getSsid() <= 0) {
                log.warn("Failed to get anchor channel info: anchorUid={}, channelInfo={}",
                        cpUid.getAnchorUid(), channelInfo);
                return;
            }

            // 2. 生成应援口令的唯一序列号
            String seq = generateSupportSloganSeq(attr, cpUid, channelInfo);

            // 3. 设置应援口令过期时间（默认30分钟）
            Date expiredTime = DateUtil.addMinutes(new Date(), 30);

            // 4. 调用ChannelWatchwordLotteryComponent添加应援口令抽奖
            channelWatchwordLotteryComponent.addWatchwordLotteryBox(
                    attr.getActId(),
                    componentIndex,
                    seq,
                    cpUid.getMember(), // 使用CP成员作为memberId
                    channelInfo.getSid(),
                    channelInfo.getSsid(),
                    expiredTime
            );

            log.info("Support slogan sent successfully: slogan={}, seq={}, sid={}, ssid={}, anchorUid={}",
                    supportSlogan, seq, channelInfo.getSid(), channelInfo.getSsid(), cpUid.getAnchorUid());

        } catch (Exception e) {
            log.error("Failed to send support slogan to room: cpUid={}", JSON.toJSONString(cpUid), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 获取主播所在房间信息
     */
    private ChannelInfoVo getAnchorChannelInfo(long anchorUid) {
        try {
            // 1. 首先尝试从在线频道服务获取
            ChannelInfoVo channelInfo = onlineChannelService.getChannelInfoVo(anchorUid);
            if (channelInfo != null && channelInfo.getSid() > 0 && channelInfo.getSsid() > 0) {
                log.info("Got anchor channel info from online service: anchorUid={}, sid={}, ssid={}",
                        anchorUid, channelInfo.getSid(), channelInfo.getSsid());
                return channelInfo;
            }

            // 2. 如果在线频道服务没有数据，尝试从用户当前频道获取
            UserCurrentChannel userChannel = commonService.getNoCacheUserCurrentChannel(anchorUid, 2);
            if (userChannel != null && userChannel.getTopsid() > 0 && userChannel.getSubsid() > 0) {
                ChannelInfoVo result = new ChannelInfoVo();
                result.setSid(userChannel.getTopsid());
                result.setSsid(userChannel.getSubsid());
                result.setTimestamp(userChannel.getIntime());

                log.info("Got anchor channel info from user current channel: anchorUid={}, sid={}, ssid={}",
                        anchorUid, result.getSid(), result.getSsid());
                return result;
            }

            log.warn("No channel info found for anchor: anchorUid={}", anchorUid);
            return null;

        } catch (Exception e) {
            log.error("Failed to get anchor channel info: anchorUid={}", anchorUid, e);
            return null;
        }
    }

    /**
     * 生成应援口令的唯一序列号
     */
    private String generateSupportSloganSeq(CpHourlyWinnerComponentAttr attr, CpUid cpUid, ChannelInfoVo channelInfo) {
        // 格式：cp_hourly_winner_{actId}_{cmptUseInx}_{hourCode}_{anchorUid}_{sid}_{ssid}
        String hourCode = DateUtil.format(new Date(), "yyyyMMddHH");
        return String.format("cp_hourly_winner_%d_%d_%s_%d_%d_%d",
                attr.getActId(),
                attr.getCmptUseInx(),
                hourCode,
                cpUid.getAnchorUid(),
                channelInfo.getSid(),
                channelInfo.getSsid());
    }

    /**
     * 广播第一名CP信息
     */
    private void broadcastWinnerInfo(CpHourlyWinnerComponentAttr attr, UserInfoWithNickExt userInfo,
                                   UserInfoWithNickExt anchorInfo, long score) {
        try {
            if (!attr.isEnableBroadcast()) {
                log.info("Broadcast is disabled, skip broadcasting");
                return;
            }

            // 构建广播数据
            Map<String, Object> broadcastData = Maps.newHashMap();
            broadcastData.put("userNick", userInfo.getNick());
            broadcastData.put("userAvatar", userInfo.getLogo());
            broadcastData.put("anchorNick", anchorInfo.getNick());
            broadcastData.put("anchorAvatar", anchorInfo.getLogo());
            broadcastData.put("score", score);
            broadcastData.put("hourTime", DateUtil.format(new Date(), "yyyyMMddHH"));

            // 根据广播模板类型选择广播方式
            Template template = getTemplateByType(attr.getBroadcastTemplate());

            // 发送横幅广播
            if (attr.getBroadcastBannerId() > 0) {
                commonBroadCastService.commonBannerBroadcast(
                        attr.getActId(),
                        anchorInfo.getUid(),
                        score,
                        "",  // svgaUrl
                        attr.getBroadcastBannerId(),
                        attr.getBroadcastBannerType(),
                        broadcastData,
                        null // channelInfos - 全服广播
                );

                log.info("Winner info broadcasted successfully: userNick={}, anchorNick={}, score={}",
                        userInfo.getNick(), anchorInfo.getNick(), score);
            }

        } catch (Exception e) {
            log.error("Failed to broadcast winner info", e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 根据类型获取广播模板
     */
    private Template getTemplateByType(int templateType) {
        switch (templateType) {
            case 2:
                return Template.Gamebaby;
            case 3:
                return Template.Jiaoyou;
            case 5:
                return Template.SkillCard;
            default:
                return Template.Gamebaby;
        }
    }

    /**
     * HTTP接口1：查询历史小时冠军CP
     */
    @RequestMapping("/queryHistoryWinners")
    public GameecologyActivity.Response queryHistoryWinners(
            @RequestParam long actId,
            @RequestParam long cmptUseInx,
            @RequestParam String dateCode) {
        
        log.info("queryHistoryWinners start: actId={}, cmptUseInx={}, dateCode={}", actId, cmptUseInx, dateCode);
        
        try {
            // TODO: 实现查询历史小时冠军CP逻辑
            List<Cmpt5159CpHourlyWinner> winners = cpHourlyWinnerComponentDao.queryHistoryWinnersByDate(actId, cmptUseInx, dateCode);
            
            // TODO: 转换为前端需要的格式
            HistoryWinnersResponse response = new HistoryWinnersResponse();
            // 设置响应数据
            
            log.info("queryHistoryWinners completed: found {} winners", winners.size());
            
            return GameecologyActivity.Response.newBuilder()
                    .setCode(0)
                    .setMsg("success")
                    .setData(JSON.toJSONString(response))
                    .build();
                    
        } catch (Exception e) {
            log.error("queryHistoryWinners failed", e);
            return GameecologyActivity.Response.newBuilder()
                    .setCode(-1)
                    .setMsg("查询失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * HTTP接口2：查询全服礼物奖池余额
     */
    @RequestMapping("/queryGiftPoolBalance")
    public GameecologyActivity.Response queryGiftPoolBalance(
            @RequestParam long actId,
            @RequestParam long cmptUseInx) {
        
        log.info("queryGiftPoolBalance start: actId={}, cmptUseInx={}", actId, cmptUseInx);
        
        try {
            // TODO: 实现查询礼物奖池余额逻辑
            Long balance = cpHourlyWinnerComponentDao.queryLatestGiftPoolBalance(actId, cmptUseInx);
            if (balance == null) {
                // 如果没有记录，返回配置中的初始余额
                CpHourlyWinnerComponentAttr attr = getComponentAttr(actId, cmptUseInx);
                balance = attr.getGiftPoolCurrentBalance();
            }
            
            GiftPoolBalanceResponse response = new GiftPoolBalanceResponse();
            response.setBalance(balance);
            response.setBalanceYuan(balance / 100.0); // 转换为元
            
            log.info("queryGiftPoolBalance completed: balance={}", balance);
            
            return GameecologyActivity.Response.newBuilder()
                    .setCode(0)
                    .setMsg("success")
                    .setData(JSON.toJSONString(response))
                    .build();
                    
        } catch (Exception e) {
            log.error("queryGiftPoolBalance failed", e);
            return GameecologyActivity.Response.newBuilder()
                    .setCode(-1)
                    .setMsg("查询失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 历史冠军查询响应对象
     */
    @Data
    public static class HistoryWinnersResponse {
        private List<WinnerInfo> winners;
        
        @Data
        public static class WinnerInfo {
            private String userNick;
            private String userAvatar;
            private String anchorNick;
            private String anchorAvatar;
            private String hourCode;
            private Long score;
        }
    }

    /**
     * 礼物奖池余额查询响应对象
     */
    @Data
    public static class GiftPoolBalanceResponse {
        private Long balance; // 余额（分）
        private Double balanceYuan; // 余额（元）
    }
}
