package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.List;

/**
 * CP小时第一名玩法组件属性配置
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Data
public class CpHourlyWinnerNewComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "CP榜单ID")
    private long cpRankId;

    @ComponentAttrField(labelText = "CP阶段ID")
    private long cpPhaseId;

    @ComponentAttrField(labelText = "发奖业务ID", dropDownSourceBeanClass = BizSource.class)
    private long awardBusiId;

    @ComponentAttrField(labelText = "分数区间奖励配置", remark = "根据第一名分数区间发放不同奖励",
            subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ScoreRangeAward.class))
    private List<ScoreRangeAward> scoreRangeAwards = Lists.newArrayList();

    @ComponentAttrField(labelText = "全服礼物奖池总限量", remark = "单位：元，使用long避免浮点数精度问题，实际值需要除以100")
    private long totalGiftPoolLimit = 4000000L; // 40000.0元 * 100

    @ComponentAttrField(labelText = "当前礼物奖池余额", remark = "单位：元，使用long避免浮点数精度问题，实际值需要除以100")
    private long currentGiftPoolBalance = 1296000L; // 12960.0元 * 100

    @ComponentAttrField(labelText = "奖池不足时替代奖励", remark = "当礼物奖池不足时发放的替代奖励")
    private AwardAttrConfig fallbackAward;

    @ComponentAttrField(labelText = "应援口令抽奖组件索引", remark = "ChannelWatchwordLotteryComponent组件的使用索引")
    private long watchwordLotteryComponentIndex;

    @ComponentAttrField(labelText = "应援口令文案")
    private String watchwordText = "夏日派对，浪漫加倍";

    @ComponentAttrField(labelText = "应援口令过期时间", remark = "单位：分钟")
    private long watchwordExpireMinutes = 60;

    @ComponentAttrField(labelText = "广播业务ID", remark = "1-语音房 2-交友房 4-其他 8-宝贝 位域表示 支持组合")
    private int broadcastBusiId = 2;

    @ComponentAttrField(labelText = "广播模板", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private int broadcastTemplate = 3;

    @ComponentAttrField(labelText = "广播横幅ID")
    private long broadcastBannerId = 5159001L;

    /**
     * 分数区间奖励配置
     */
    @Data
    public static class ScoreRangeAward {
        /**
         * 最小分数
         */
        private long minScore;

        /**
         * 最大分数
         */
        private long maxScore;

        /**
         * 奖励配置
         */
        private AwardAttrConfig awardAttrConfig;

        /**
         * 检查分数是否在此区间内
         */
        public boolean isInRange(long score) {
            return score >= minScore && (maxScore <= 0 || score <= maxScore);
        }
    }

    /**
     * 根据分数获取对应的奖励配置
     */
    public ScoreRangeAward getAwardByScore(long score) {
        for (ScoreRangeAward award : scoreRangeAwards) {
            if (award.isInRange(score)) {
                return award;
            }
        }
        return null;
    }

    /**
     * 检查礼物奖池是否充足
     */
    public boolean isGiftPoolSufficient(long requiredAmount) {
        return currentGiftPoolBalance >= requiredAmount;
    }

    /**
     * 扣减礼物奖池余额
     */
    public void deductGiftPool(long amount) {
        currentGiftPoolBalance = Math.max(0, currentGiftPoolBalance - amount);
    }

    /**
     * 获取礼物奖池余额（元）
     */
    public double getGiftPoolBalanceInYuan() {
        return currentGiftPoolBalance / 100.0;
    }

    /**
     * 获取礼物奖池总限量（元）
     */
    public double getTotalGiftPoolLimitInYuan() {
        return totalGiftPoolLimit / 100.0;
    }
}
