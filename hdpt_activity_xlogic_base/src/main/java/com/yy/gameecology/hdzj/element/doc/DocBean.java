package com.yy.gameecology.hdzj.element.doc;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/18 14:37
 **/
@Data
public class DocBean {
    private String name;
    private String commentText;
    private String author;

    private List<FieldCommentBean> fieldCommentBeanList = new ArrayList<>();
    private List<MethodCommentBean> methodCommentBeanList = new ArrayList<>();

    public void addMethodCommentBean(MethodCommentBean methodCommentBean) {
        methodCommentBeanList.add(methodCommentBean);
    }

    public void addFieldCommentBean(FieldCommentBean fieldCommentBean) {
        fieldCommentBeanList.add(fieldCommentBean);
    }
}
