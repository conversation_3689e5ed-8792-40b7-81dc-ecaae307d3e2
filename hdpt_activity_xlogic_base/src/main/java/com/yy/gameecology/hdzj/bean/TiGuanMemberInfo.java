package com.yy.gameecology.hdzj.bean;

import com.yy.gameecology.activity.bean.ChannelInfoVo;

public class TiGuanMemberInfo implements Comparable<TiGuanMemberInfo> {
    // 成员标识
    private String memberId;

    // 成员昵称
    private String nickname = "";

    // 成员头像
    private String logo = "";

    // 成员当前分值
    private long score;

    // 比拼结果， 0：未定（未结算）， 1：胜者， -1：败者
    private long status = 0;

    // 结算奖励值, 根据业务要求和前端约定使用
    private long awardNum = 0;

    //额外奖励,种子主播奖励
    private long extAwardNum = 0;

    //种子主播 1->是 2->否
    private int seedAnchor = 2;

    private ChannelInfoVo channelInfo;

    public TiGuanMemberInfo(){
    }

    public TiGuanMemberInfo(String memberId, long score) {
        this.memberId = memberId;
        this.score = score;
    }

    public TiGuanMemberInfo(String memberId, long score, long status) {
        this.memberId = memberId;
        this.score = score;
        this.status = status;
    }

    public TiGuanMemberInfo(String memberId, long score, long status, long awardNum) {
        this.memberId = memberId;
        this.score = score;
        this.status = status;
        this.awardNum = awardNum;
    }

    public ChannelInfoVo getChannelInfo() {
        return channelInfo;
    }

    public void setChannelInfo(ChannelInfoVo channelInfo) {
        this.channelInfo = channelInfo;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public long getScore() {
        return score;
    }

    public void setScore(long score) {
        this.score = score;
    }

    public long getStatus() {
        return status;
    }

    public void setStatus(long status) {
        this.status = status;
    }

    public long getAwardNum() {
        return awardNum;
    }

    public void setAwardNum(long awardNum) {
        this.awardNum = awardNum;
    }

    public long getExtAwardNum() {
        return extAwardNum;
    }

    public void setExtAwardNum(long extAwardNum) {
        this.extAwardNum = extAwardNum;
    }

    public int getSeedAnchor() {
        return seedAnchor;
    }

    public void setSeedAnchor(int seedAnchor) {
        this.seedAnchor = seedAnchor;
    }

    @Override
    public int compareTo(TiGuanMemberInfo o) {
        if(this.score < o.score) {
            return -1;
        }
        if(this.score > o.score) {
            return 1;
        }
        return  0;
    }
}
