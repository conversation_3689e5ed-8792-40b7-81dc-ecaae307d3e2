package com.yy.gameecology.hdzj.element.history.attr;

import com.yy.gameecology.activity.bean.hdzt.HdztAwardConfig;
import com.yy.gameecology.hdzj.annotation.SkipCheck;
import com.yy.gameecology.hdzj.element.ComponentAttr;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-12-13 15:08
 **/
@SkipCheck
public class WishingBottleComponentAttr extends ComponentAttr {
    /**
     * 400==宝贝 500==交友 600==约战
     */
    private long busiId;

    /**
     * 过任务榜单id
     */
    private long rankId;

    /**
     * 许愿瓶2级任务榜单id
     */
    private long task2RankId;

    /**
     * 许愿瓶2级任务阶段id
     */
    private long task2PhaseId;


    /**
     * 贡献榜单对应id
     */
    private long contributeRankId;

    /**
     * 取贡献榜top n
     */
    private int contributeTopN;

    /**
     * 阶段id
     */
    private long phaseId;

    /**
     * 单个许愿瓶抽奖可持续时间
     *
     * 这个配置不能大于20秒，因为目前客户端动画是20秒，如果要改成大于20，需要和客户端俊杰确认
     */
    private long boxEffectiveSeconds = 20;

    /**
     * 不同瓶子排队间隔延迟,最终延迟时间是boxEffectiveSeconds+boxQueueDelaySeconds
     */
    private long boxQueueDelaySeconds = 11;

    /**
     * 单个许愿瓶抽奖请求到来可持续时间允许时延
     */
    private long boxEffectiveDelaySeconds = 60;

    /**
     * 送礼符合某个数量，获得稀有祝福送礼资格配置礼物配置，key 礼物id value礼物个数
     */
    private Map<String, Long> rareAwardUserGift;

    /**
     * 稀有祝福中奖比例
     */
    private int rareAwardBingoRatio = 1;
    private int rareAwardTotalRatio = 3;

    /**
     * 稀缺祝福中台发奖配置 taskid packageid
     */
    private HdztAwardConfig rareAwardConfig;

    /**
     * 普通祝福 奖品抽奖 taskId packageId
     */
    private HdztAwardConfig commonAwardConfig;

    /**
     * 祝福奖包id和中控自己祝福礼物映射关系
     */
    private Map<Long, String> packageIdGiftCodeMap;

    /**
     * 要啥有啥  稀缺祝福礼物编码
     * <p>
     * 一夜暴富==1、平安喜乐==2、升职加薪==3、锦鲤附体==4、只吃不胖==5
     */
    private String rareAwardGiftCode = "999";

    /**
     * 用户集齐所有卡的奖励中台发奖配置
     */
    private Map<Long, Map<Long, Integer>> collectAllAward;

    /**
     * 许愿次数累榜礼物
     */
    private String updateRankingItemId;

    /**
     * 可参与瓜分奖池的主播完成的任务等级数
     */
    private long canCarveTaskIndex;

    /**
     * 瓜分奖池资格 累榜礼物
     */
    private String carveUpdateRankingItem;

    private String collectAllTips ="恭喜%s达成新年成就，闪亮全场!";


    public long getRankId() {
        return rankId;
    }

    public void setRankId(long rankId) {
        this.rankId = rankId;
    }

    public long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(long phaseId) {
        this.phaseId = phaseId;
    }

    public long getContributeRankId() {
        return contributeRankId;
    }

    public void setContributeRankId(long contributeRankId) {
        this.contributeRankId = contributeRankId;
    }

    public int getContributeTopN() {
        return contributeTopN;
    }

    public void setContributeTopN(int contributeTopN) {
        this.contributeTopN = contributeTopN;
    }

    public long getBoxEffectiveSeconds() {
        return boxEffectiveSeconds;
    }

    public void setBoxEffectiveSeconds(long boxEffectiveSeconds) {
        this.boxEffectiveSeconds = boxEffectiveSeconds;
    }

    public long getBoxEffectiveDelaySeconds() {
        return boxEffectiveDelaySeconds;
    }

    public void setBoxEffectiveDelaySeconds(long boxEffectiveDelaySeconds) {
        this.boxEffectiveDelaySeconds = boxEffectiveDelaySeconds;
    }

    public int getRareAwardBingoRatio() {
        return rareAwardBingoRatio;
    }

    public void setRareAwardBingoRatio(int rareAwardBingoRatio) {
        this.rareAwardBingoRatio = rareAwardBingoRatio;
    }

    public int getRareAwardTotalRatio() {
        return rareAwardTotalRatio;
    }

    public void setRareAwardTotalRatio(int rareAwardTotalRatio) {
        this.rareAwardTotalRatio = rareAwardTotalRatio;
    }

    public String getRareAwardGiftCode() {
        return rareAwardGiftCode;
    }

    public void setRareAwardGiftCode(String rareAwardGiftCode) {
        this.rareAwardGiftCode = rareAwardGiftCode;
    }

    public HdztAwardConfig getRareAwardConfig() {
        return rareAwardConfig;
    }

    public void setRareAwardConfig(HdztAwardConfig rareAwardConfig) {
        this.rareAwardConfig = rareAwardConfig;
    }

    public HdztAwardConfig getCommonAwardConfig() {
        return commonAwardConfig;
    }

    public void setCommonAwardConfig(HdztAwardConfig commonAwardConfig) {
        this.commonAwardConfig = commonAwardConfig;
    }

    public Map<Long, String> getPackageIdGiftCodeMap() {
        return packageIdGiftCodeMap;
    }

    public void setPackageIdGiftCodeMap(Map<Long, String> packageIdGiftCodeMap) {
        this.packageIdGiftCodeMap = packageIdGiftCodeMap;
    }

    public Map<Long, Map<Long, Integer>> getCollectAllAward() {
        return collectAllAward;
    }

    public void setCollectAllAward(Map<Long, Map<Long, Integer>> collectAllAward) {
        this.collectAllAward = collectAllAward;
    }

    public long getBusiId() {
        return busiId;
    }

    public void setBusiId(long busiId) {
        this.busiId = busiId;
    }

    public String getUpdateRankingItemId() {
        return updateRankingItemId;
    }

    public void setUpdateRankingItemId(String updateRankingItemId) {
        this.updateRankingItemId = updateRankingItemId;
    }

    public Map<String, Long> getRareAwardUserGift() {
        return rareAwardUserGift;
    }

    public void setRareAwardUserGift(Map<String, Long> rareAwardUserGift) {
        this.rareAwardUserGift = rareAwardUserGift;
    }

    public long getCanCarveTaskIndex() {
        return canCarveTaskIndex;
    }

    public void setCanCarveTaskIndex(long canCarveTaskIndex) {
        this.canCarveTaskIndex = canCarveTaskIndex;
    }

    public String getCarveUpdateRankingItem() {
        return carveUpdateRankingItem;
    }

    public void setCarveUpdateRankingItem(String carveUpdateRankingItem) {
        this.carveUpdateRankingItem = carveUpdateRankingItem;
    }

    public long getTask2RankId() {
        return task2RankId;
    }

    public void setTask2RankId(long task2RankId) {
        this.task2RankId = task2RankId;
    }

    public long getTask2PhaseId() {
        return task2PhaseId;
    }

    public void setTask2PhaseId(long task2PhaseId) {
        this.task2PhaseId = task2PhaseId;
    }

    public long getBoxQueueDelaySeconds() {
        return boxQueueDelaySeconds;
    }

    public void setBoxQueueDelaySeconds(long boxQueueDelaySeconds) {
        this.boxQueueDelaySeconds = boxQueueDelaySeconds;
    }

    public String getCollectAllTips() {
        return collectAllTips;
    }

    public void setCollectAllTips(String collectAllTips) {
        this.collectAllTips = collectAllTips;
    }
}
