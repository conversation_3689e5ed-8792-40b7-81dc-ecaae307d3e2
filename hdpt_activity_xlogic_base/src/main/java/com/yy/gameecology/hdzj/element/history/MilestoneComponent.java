package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.actlayer.MemberInfo;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.MemberInfoService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.RankExtParaKey;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.MyListUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.MilestoneComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基于陪玩团,目前陪玩业务已经不做活动,先暂时移动到历史文件夹
 * 里程碑
 *
 * <AUTHOR>
 * @date 2021.06.21 11:17
 */
@Component
@Deprecated
public class MilestoneComponent extends BaseActComponent<MilestoneComponentAttr> {

    private Logger logger = LoggerFactory.getLogger(MilestoneComponent.class);

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private MemberInfoService memberInfoService;

    @Override
    public Long getComponentId() {
        return ComponentId.MILE_STONE;
    }

    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = false)
    public void onPhaseTimeEndEvent(PhaseTimeEnd event, MilestoneComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (rankId != attr.getMainRankId() || phaseId != attr.getPhaseId()) {
            return;
        }


        log.info(" MILE_STONE onPhaseTimeEndEvent begin,event:{},attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        long actId = attr.getActId();
        long cmptUseInx = attr.getCmptUseInx();
        long thresholdScore = attr.getScore();
        int level = (int) attr.getLevel();

        String endTime = event.getEndTime();
        Date eventDate = DateUtil.getDate(endTime);
        String dateStr = TimeKeyHelper.getTimeCode(event.getTimeKey(), eventDate);

        //获取符合要求的陪玩团榜单数据
        List<Rank> ranks = queryRanksByThreshold(actId, rankId, phaseId, dateStr, 100, thresholdScore);

        Map<String, QueryRankingRequest> playerRequests = Maps.newHashMap();
        Map<String, QueryRankingRequest> anchorRequests = Maps.newHashMap();

        for (Rank rank : ranks) {
            String teamId = rank.getMember();
            long score = Convert.toLong(rank.getScore());

            if (score < thresholdScore) {
                continue;
            }

            playerRequests.put(teamId, setupRequest(actId, phaseId, attr.getPlayerRankId(), dateStr, attr.getPlayerTopN(), teamId));
            anchorRequests.put(teamId, setupRequest(actId, phaseId, attr.getAnchorRankId(), dateStr, attr.getAnchorTopN(), teamId));

            log.info("milestoneComponent onPhaseTimeEndEvent info@actId:{} index:{} teamId:{} score:{} level:{}",
                    actId, cmptUseInx, teamId, score, level);

            bigDataService.saveNoRankDataToFile(actId, BusiId.PEI_WAN, eventDate.getTime(), teamId, RoleType.PWTUAN, level
                    , 15, "", 0, score);
        }

        award(playerRequests, anchorRequests, eventDate, attr);

    }

    /**
     * 全业务广播横幅
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = false)
    public void doMilestoneBroadcast(TaskProgressChanged event, MilestoneComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (rankId != attr.getMainRankId() || phaseId != attr.getPhaseId() || !attr.isBroadcast()) {
            return;
        }
        String teamId = event.getMember();
        doMilestoneBroadcastTop(attr.getActId(), teamId);
        log.info("doMilestoneBroadcast done@actId:{},index:{},level:{},teamId:{}"
                , attr.getActId(), attr.getCmptUseInx(), attr.getLevel(), teamId);
    }

    private void award(Map<String, QueryRankingRequest> playerRequests, Map<String, QueryRankingRequest> anchorRequests, Date eventDate, MilestoneComponentAttr attr) {

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> {
            Clock clock = new Clock();
            long actId = attr.getActId();
            long cmptUseInx = attr.getCmptUseInx();
            Map<String, BatchRankingItem> playerMap = hdztRankingThriftClient.queryBatchRanking(playerRequests, null);
            Map<String, BatchRankingItem> anchorMap = hdztRankingThriftClient.queryBatchRanking(anchorRequests, null);

            String time = DateUtil.getNowYyyyMMddHHmmss();

            for (Map.Entry<String, BatchRankingItem> entry : playerMap.entrySet()) {
                List<Rank> ranks = entry.getValue().getData();
                String hoverMemberId = entry.getKey();
                if (CollectionUtils.isEmpty(ranks)) {
                    log.error("milestoneComponent award ignore not find player rank@actId:{} index:{} hoverMemberId:{}",
                            actId, cmptUseInx, hoverMemberId);
                    continue;
                }
                for (Rank rank : ranks) {
                    hdztAwardServiceClient.doBatchWelfare(Convert.toLong(rank.getMember()), attr.getPlayerAwardPackageIds(), time, attr.getRetry(), Maps.newHashMap());
                    bigDataService.saveNoRankDataToFile(actId, BusiId.PEI_WAN, eventDate.getTime(), rank.getMember(), RoleType.USER, attr.getLevel()
                            , 15, hoverMemberId);
                }
            }

            for (Map.Entry<String, BatchRankingItem> entry : anchorMap.entrySet()) {
                List<Rank> ranks = entry.getValue().getData();
                String hoverMemberId = entry.getKey();
                if (CollectionUtils.isEmpty(ranks)) {
                    log.error("milestoneComponent award ignore not find anchor rank@actId:{} index:{} hoverMemberId:{}",
                            actId, cmptUseInx, hoverMemberId);
                    continue;
                }
                for (Rank rank : ranks) {
                    hdztAwardServiceClient.doBatchWelfare(Convert.toLong(rank.getMember()), attr.getAnchorAwardPackageIds(), time, attr.getRetry(), Maps.newHashMap());
                    bigDataService.saveNoRankDataToFile(actId, BusiId.PEI_WAN, eventDate.getTime(), rank.getMember(), RoleType.ANCHOR, attr.getLevel()
                            , 15, hoverMemberId);
                }
            }

            logger.info("milestone award finish :{} ", clock.tag());
        });
    }

    private QueryRankingRequest setupRequest(long actId, long phaseId, long rankId, String dateStr, long topN, String teamId) {
        QueryRankingRequest request = new QueryRankingRequest();
        request.setActId(actId);
        request.setRankingId(rankId);
        request.setPhaseId(phaseId);
        request.setDateStr(dateStr);
        request.setRankingCount(topN);
        Map<String, String> ext = Maps.newHashMap();
        ext.put(RankExtParaKey.RANK_TYPE_HOVER_SRC_ID, teamId);
        request.setExtData(ext);
        return request;
    }

    /**
     * 做全频道广播
     *
     * <AUTHOR>
     */
    private void doMilestoneBroadcastTop(long actId, String teamId) {
        MemberInfo memberInfo = memberInfoService.getPwTeam(teamId);
        long asid = Convert.toLong(memberInfo.getAsid());
        GameecologyActivity.Act202011_PwMilestoneBanner.Builder pwMilestoneBanner =
                GameecologyActivity.Act202011_PwMilestoneBanner.newBuilder()
                        .setTeamName(memberInfo.getName())
                        .setAsid(asid).setActId(actId);

        GameecologyActivity.GameEcologyMsg message = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setAct202011PwMilestoneBanner(pwMilestoneBanner)
                .setUri(GameecologyActivity.PacketType.kAct202011_PwMilestoneBanner_VALUE)
                .build();
        svcSDKService.broadcastAllChanelsInPW(actId, message);
        logger.info("doMilestoneBroadcastTop done teamId:{} asid:{}", teamId, asid);
    }

    /**
     * 查询 符合门槛的 榜单List
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param querySize
     * @param threshold
     * @return
     */
    private List<Rank> queryRanksByThreshold(long actId, long rankId, long phaseId, String dateStr, int querySize, long threshold) {
        List<Rank> ranks;
        Clock clock = new Clock();
        do {
            ranks = hdztRankingThriftClient.queryRanking(actId, rankId, phaseId, dateStr, querySize, Maps.newHashMap());
            long lastValue = Optional.ofNullable(MyListUtils.last(ranks)).map(Rank::getScore).orElse(0L);
            //最后一名的流水币阈值或者榜单已经没有数据
            if (lastValue < threshold || ranks.size() < querySize) {
                break;
            }
            //最后一名比阈值高并且后面还有数据的，重新查一次
            querySize *= 2;
            clock.tag();
        } while (true);

        List<Rank> newRanks = ranks.stream()
                .filter(rank -> rank.getScore() >= threshold)
                .collect(Collectors.toList());

        log.info("queryRanksByThreshold done@actId:{} rankId:{},phaseId:{},weekDay:{},querySize:{},ranks size:{} new ranks size:{} clock:{}",
                actId, rankId, phaseId, dateStr, querySize, ranks.size(), newRanks.size(), clock.tag());

        return newRanks;
    }

}
