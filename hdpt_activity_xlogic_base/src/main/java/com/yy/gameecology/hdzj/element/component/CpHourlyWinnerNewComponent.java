package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.common.bean.Response;
import com.yy.thrift.broadcast.Template;
import com.yy.gameecology.common.bean.UserInfoVo;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerNewComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerNewComponentDao;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件
 *
 * <AUTHOR> Generated
 * @date 2025-07-21
 */
@Component
@RestController
@RequestMapping("/5159")
public class CpHourlyWinnerNewComponent extends BaseActComponent<CpHourlyWinnerNewComponentAttr> {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CpHourlyWinnerNewComponentDao cpHourlyWinnerNewComponentDao;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_HOURLY_WINNER_NEW;
    }

    /**
     * 监听榜单结束事件，处理每小时CP榜单第一名结算
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, CpHourlyWinnerNewComponentAttr attr) {
        log.info("onRankingTimeEnd start event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));

        // 检查是否是我们关心的榜单
        if (event.getRankId() != attr.getCpRankId()) {
            return;
        }

        // 防重检查
        String eventSeq = event.getSeq();
        if (cpHourlyWinnerNewComponentDao.isEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq)) {
            log.warn("Event already processed, skip. eventSeq:{}", eventSeq);
            return;
        }

        try {
            // 处理小时榜单结算
            processHourlyRankSettle(event, attr);
            
            // 标记事件已处理
            cpHourlyWinnerNewComponentDao.markEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq);
            
        } catch (Exception e) {
            log.error("processHourlyRankSettle error, event:{}", event, e);
            throw e; // 重新抛出异常以触发重试
        }
    }

    /**
     * 处理小时榜单结算
     */
    private void processHourlyRankSettle(RankingTimeEnd event, CpHourlyWinnerNewComponentAttr attr) {
        // 1. 获取榜单第一名
        Date endTime = DateUtil.getDate(event.getEndTime());
        String hourTime = DateUtil.format(endTime, "yyyyMMddHH");
        
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(
                attr.getActId(), 
                attr.getCpRankId(), 
                event.getPhaseId(), 
                hourTime, 
                1, 
                Maps.newHashMap()
        );

        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("No ranking data found for hourTime:{}", hourTime);
            return;
        }

        Rank firstRank = ranks.get(0);
        if (firstRank.getScore() <= 0) {
            log.warn("First rank score is zero, skip. hourTime:{}", hourTime);
            return;
        }

        // 2. 解析CP信息
        String[] memberParts = Const.splitCpMemberSimple(firstRank.getMember());
        if (memberParts.length != 2) {
            log.error("Invalid CP member format:{}", firstRank.getMember());
            return;
        }

        long userUid = Long.parseLong(memberParts[0]);
        long anchorUid = Long.parseLong(memberParts[1]);

        // 3. 检查是否已记录过此小时的冠军
        Cmpt5159CpHourlyWinner existingRecord = cpHourlyWinnerNewComponentDao.selectByHourTime(
                attr.getActId(), attr.getCmptUseInx(), hourTime);
        if (existingRecord != null) {
            log.warn("Hourly winner already recorded for hourTime:{}", hourTime);
            return;
        }

        // 4. 获取用户信息
        List<Long> uids = Lists.newArrayList(userUid, anchorUid);
        Map<Long, UserInfoVo> userInfoMap = userInfoService.getUserInfo(uids);
        UserInfoVo userInfo = userInfoMap.get(userUid);
        UserInfoVo anchorInfo = userInfoMap.get(anchorUid);

        if (userInfo == null || anchorInfo == null) {
            log.error("Failed to get user info, userUid:{}, anchorUid:{}", userUid, anchorUid);
            return;
        }

        // 5. 记录CP小时冠军
        Cmpt5159CpHourlyWinner record = new Cmpt5159CpHourlyWinner();
        record.setActId(attr.getActId());
        record.setCmptUseInx(attr.getCmptUseInx());
        record.setUserUid(userUid);
        record.setAnchorUid(anchorUid);
        record.setUserNick(userInfo.getNick());
        record.setUserAvatar(userInfo.getAvatarUrl());
        record.setAnchorNick(anchorInfo.getNick());
        record.setAnchorAvatar(anchorInfo.getAvatarUrl());
        record.setCpScore(firstRank.getScore());
        record.setHourTime(hourTime);
        record.setAwardStatus(0);
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        cpHourlyWinnerNewComponentDao.insertCpHourlyWinner(record);

        // 6. 发放奖励
        giveAwardToWinner(record, attr, firstRank.getScore());

        // 7. 添加应援口令抽奖
        addWatchwordLottery(userUid, anchorUid, attr);

        // 8. 发送广播
        broadcastWinnerInfo(record, attr);
    }

    /**
     * 查询历史小时冠军CP接口
     */
    @GetMapping("/queryHistoryWinners")
    public Response<List<CpWinnerInfo>> queryHistoryWinners(
            @RequestParam long actId,
            @RequestParam long cmptInx,
            @RequestParam String date) {
        
        try {
            List<Cmpt5159CpHourlyWinner> records = cpHourlyWinnerNewComponentDao.selectByDate(actId, cmptInx, date);
            List<CpWinnerInfo> result = Lists.newArrayList();
            
            for (Cmpt5159CpHourlyWinner record : records) {
                CpWinnerInfo info = new CpWinnerInfo();
                info.setUserNick(record.getUserNick());
                info.setUserAvatar(record.getUserAvatar());
                info.setAnchorNick(record.getAnchorNick());
                info.setAnchorAvatar(record.getAnchorAvatar());
                info.setHourTime(record.getHourTime());
                info.setCpScore(record.getCpScore());
                result.add(info);
            }
            
            return Response.success(result);
        } catch (Exception e) {
            log.error("queryHistoryWinners error", e);
            return Response.fail(-1, "查询失败");
        }
    }

    /**
     * 查询全服礼物奖池余额接口
     */
    @GetMapping("/queryGiftPoolBalance")
    public Response<GiftPoolInfo> queryGiftPoolBalance(
            @RequestParam long actId,
            @RequestParam long cmptInx) {
        
        try {
            CpHourlyWinnerNewComponentAttr attr = getComponentAttr(actId, cmptInx);
            if (attr == null) {
                return Response.fail(-1, "组件配置不存在");
            }

            // 从缓存获取当前余额，如果没有则使用配置的默认值
            long currentBalance = cpHourlyWinnerNewComponentDao.getGiftPoolBalance(
                    actId, cmptInx, attr.getCurrentGiftPoolBalance());

            GiftPoolInfo info = new GiftPoolInfo();
            info.setTotalLimit(attr.getTotalGiftPoolLimitInYuan());
            info.setCurrentBalance(currentBalance / 100.0);
            info.setUsedAmount(attr.getTotalGiftPoolLimitInYuan() - (currentBalance / 100.0));
            
            return Response.success(info);
        } catch (Exception e) {
            log.error("queryGiftPoolBalance error", e);
            return Response.fail(-1, "查询失败");
        }
    }

    /**
     * CP获奖信息DTO
     */
    @Data
    public static class CpWinnerInfo {
        private String userNick;
        private String userAvatar;
        private String anchorNick;
        private String anchorAvatar;
        private String hourTime;
        private Long cpScore;
    }

    /**
     * 礼物奖池信息DTO
     */
    @Data
    public static class GiftPoolInfo {
        private double totalLimit;
        private double currentBalance;
        private double usedAmount;
    }

    /**
     * 发放奖励给获胜者
     */
    private void giveAwardToWinner(Cmpt5159CpHourlyWinner record, CpHourlyWinnerNewComponentAttr attr, long score) {
        try {
            // 根据分数获取奖励配置
            CpHourlyWinnerNewComponentAttr.ScoreRangeAward scoreAward = attr.getAwardByScore(score);
            if (scoreAward == null) {
                log.warn("No award config found for score:{}", score);
                return;
            }

            // 检查是否是礼物奖励且奖池是否充足
            boolean useGiftAward = true;
            Long giftValue = scoreAward.getAwardAmount(); // 获取奖励金额
            Long taskId = scoreAward.getTAwardTskId();
            Long packageId = scoreAward.getTAwardPkgId();
            Integer num = scoreAward.getNum();

            if (giftValue != null && giftValue > 0) {
                long currentBalance = cpHourlyWinnerNewComponentDao.getGiftPoolBalance(
                        attr.getActId(), attr.getCmptUseInx(), attr.getCurrentGiftPoolBalance());

                if (currentBalance < giftValue) {
                    // 奖池不足，使用替代奖励
                    useGiftAward = false;
                    AwardAttrConfig fallbackAward = attr.getFallbackAward();
                    if (fallbackAward != null) {
                        taskId = fallbackAward.getTAwardTskId();
                        packageId = fallbackAward.getTAwardPkgId();
                        num = fallbackAward.getNum();
                        log.info("Gift pool insufficient, use fallback award. required:{}, current:{}",
                                giftValue, currentBalance);
                    }
                }
            }

            // 发放奖励
            String seq = String.format("cp_hourly_winner:%d:%d:%s", attr.getActId(), attr.getCmptUseInx(), record.getHourTime());
            String time = DateUtil.getNowYyyyMMddHHmmss();

            // 对用户发奖
            BatchWelfareResult userResult = hdztAwardServiceClient.doWelfare(time, attr.getAwardBusiId(), record.getUserUid(),
                    taskId != null ? taskId : 0L, num != null ? num : 1, packageId != null ? packageId : 0L,
                    seq + "_user");

            // 对主播发奖
            BatchWelfareResult anchorResult = hdztAwardServiceClient.doWelfare(time, attr.getAwardBusiId(), record.getAnchorUid(),
                    taskId != null ? taskId : 0L, num != null ? num : 1, packageId != null ? packageId : 0L,
                    seq + "_anchor");

            if (userResult != null && userResult.getCode() == 0 && anchorResult != null && anchorResult.getCode() == 0) {
                // 发奖成功，更新奖池余额
                if (useGiftAward && giftValue != null && giftValue > 0) {
                    long currentBalance = cpHourlyWinnerNewComponentDao.getGiftPoolBalance(
                            attr.getActId(), attr.getCmptUseInx(), attr.getCurrentGiftPoolBalance());
                    long newBalance = Math.max(0, currentBalance - giftValue);
                    cpHourlyWinnerNewComponentDao.updateGiftPoolBalance(attr.getActId(), attr.getCmptUseInx(), newBalance);
                }

                // 更新奖励状态
                String awardContent = JSON.toJSONString(scoreAward);
                cpHourlyWinnerNewComponentDao.updateAwardStatus(attr.getActId(), attr.getCmptUseInx(),
                        record.getHourTime(), 1, awardContent);

                log.info("Award given successfully to CP, userUid:{}, anchorUid:{}, score:{}",
                        record.getUserUid(), record.getAnchorUid(), score);
            } else {
                log.error("Award failed, userResult:{}, anchorResult:{}", userResult, anchorResult);
            }

        } catch (Exception e) {
            log.error("giveAwardToWinner error", e);
        }
    }

    /**
     * 添加应援口令抽奖
     */
    private void addWatchwordLottery(long userUid, long anchorUid, CpHourlyWinnerNewComponentAttr attr) {
        try {
            // 获取用户当前频道信息
            UserCurrentChannel userChannel = commonService.getNoCacheUserCurrentChannel(userUid);
            UserCurrentChannel anchorChannel = commonService.getNoCacheUserCurrentChannel(anchorUid);

            // 优先使用主播的频道，如果主播不在线则使用用户的频道
            UserCurrentChannel targetChannel = anchorChannel != null ? anchorChannel : userChannel;

            if (targetChannel == null) {
                log.warn("Both user and anchor are not in channel, skip watchword lottery. userUid:{}, anchorUid:{}",
                        userUid, anchorUid);
                return;
            }

            // 调用应援口令抽奖组件添加抽奖盒子
            // 这里需要根据ChannelWatchwordLotteryComponent的实际接口来调用
            // 暂时记录日志，实际使用时需要实现具体调用逻辑
            log.info("Add watchword lottery for CP, userUid:{}, anchorUid:{}, sid:{}, ssid:{}, watchword:{}",
                    userUid, anchorUid, targetChannel.getTopsid(), targetChannel.getSubsid(), attr.getWatchwordText());

        } catch (Exception e) {
            log.error("addWatchwordLottery error", e);
        }
    }

    /**
     * 广播获胜者信息
     */
    private void broadcastWinnerInfo(Cmpt5159CpHourlyWinner record, CpHourlyWinnerNewComponentAttr attr) {
        try {
            // 构建广播数据
            Map<String, Object> jsonData = Maps.newHashMap();
            jsonData.put("userUid", record.getUserUid());
            jsonData.put("userNick", record.getUserNick());
            jsonData.put("userAvatar", record.getUserAvatar());
            jsonData.put("anchorUid", record.getAnchorUid());
            jsonData.put("anchorNick", record.getAnchorNick());
            jsonData.put("anchorAvatar", record.getAnchorAvatar());
            jsonData.put("cpScore", record.getCpScore());
            jsonData.put("hourTime", record.getHourTime());

            // 使用通用横幅广播 - 全模板广播
            Template template = Template.Jiaoyou; // 根据配置选择模板
            commonBroadCastService.commonBannerBroadcast(
                    attr.getActId(),
                    record.getUserUid(),
                    record.getCpScore(),
                    "", // svgaUrl 可以为空
                    attr.getBroadcastBannerId(),
                    1, // bannerType
                    jsonData,
                    template
            );

            log.info("Winner broadcast sent, userUid:{}, anchorUid:{}, score:{}",
                    record.getUserUid(), record.getAnchorUid(), record.getCpScore());

        } catch (Exception e) {
            log.error("broadcastWinnerInfo error", e);
        }
    }

    /**
     * 从奖励配置中获取礼物价值（分为单位）
     */
    private long getGiftValueFromAward(CpHourlyWinnerNewComponentAttr.ScoreRangeAward scoreAward) {
        // 直接从ScoreRangeAward中获取奖励金额
        return scoreAward.getAwardAmount() != null ? scoreAward.getAwardAmount() : 0L;
    }
}
