package com.yy.gameecology.hdzj.bean.pepc;

import com.yy.gameecology.common.consts.PepcConst;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc:
 *
 * <AUTHOR>
 * @date 2025-04-08 15:30
 **/
@Data
public class GroupGameLiveItemVo {
    private long gameId;
    /**
     * 直播状态
     * @see PepcConst.GameLiveState
     */
    private int state;

    /**
     * 游戏状态
     * @see PepcConst.LiveGameState
     */
    private int gameState;

    private long startTime;
    private String gameLiveInfo;
}
