package com.yy.apphistory.y2022.act2022046003.bean;

import lombok.Data;

import java.util.List;

/**
 * 每日经验信息
 *
 * <AUTHOR>
 * @date 2022/3/30 10:09
 **/
@Data
public class DailyExperienceInfo {
    /**
     * 当前活动名称
     **/
    private String currentActName;
    /**
     * 当前活动id
     **/
    private long currentActId;
    /**
     * 礼物id
     **/
    private List<String> giftIcons;
    /**
     * 活动跳转地址
     **/
    private String actUrl;
    /**
     * 任务信息
     **/
    private List<ExperienceTaskInfo> taskInfos;
    /**
     * 是否展示加倍信息
     **/
    private boolean showDoubleInfo;

    /**
     * 经验限制
     **/
    private long experienceLimit;

    /**
     * 已获得经验
     **/
    private long experience;
    /**
     * 累计威望值
     **/
    private long prestigeValue;
}
