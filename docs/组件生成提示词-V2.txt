实现这个组件
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/hxt1wNz2QRyrfo


------------------------------------------------------------------------------
------------------------------->第1轮对话<-------------------------------------
------------------------------------------------------------------------------
组件开发分为以下3个步骤：
1、数据库设计

2、组件配置设计规划

3、方法分解、实现

根据component_ai.md中的规范，创建cp小时第一名玩法组件的代码文件，实现以下功能：
------------------------------------------------------------------------------

数据库设计：
1. 设计1个mysql表，用于存储每小时cp榜单的第一名

------------------------------------------------------------------------------
程序实现：

榜单结束事件结算方法：
1. 每小时结束时，记录CP榜单第一名。
2. 对第一名CP发放奖励，根据第一分数区间不同，发放不一样的奖励，发奖奖励可配置在组件属性类。
3. 全服礼物奖池总限量40000.0元（当前礼物剩余12960元），若CP获得礼物奖励时，剩余礼物不足，则将以夏日飞骏进场秀3天形式进行发放。奖池限额以及奖池不足时发放的礼物配置，放到组件属性配置。
3. 对第一名CP所在的房间，发送应援口令“夏日派对，浪漫加倍”，可参与抽奖，有机会获得进场秀、头像框等奖励，这个功能的实现，调用ChannelWatchwordLotteryComponent组件实现。
4. 每小时结束时广播第一名CP信息，广播信息包括用户头像、用户昵称、主播头像、主播昵称


http接口：
接口1. 提供查询历史小时冠军CP接口，传入参数为，活动id、组件索引、日期，返回结果为用户昵称、用户头像、主播昵称、主播头像
接口2. 查询全服礼物奖池余额接口，传入参数为，活动id、组件索引，返回结果为奖池余额

------------------------------------------------------------------------------
相对比v1已解决问题：
1、cp成员分割方式

------------------------------------------------------------------------------
------------------------------->第2轮对话<-------------------------------------
------------------------------------------------------------------------------
生成的代码存在以下问题：
1、CpHourlyWinnerNewComponent中，import的包名不对，导致代码报错了
此问题v1已经存在，v2仍然未解决
2、组件属性定义没有按照约束来做，按 照组件属性编码规范文档.md 说明再重新定义一下CpHourlyWinnerNewComponentAttr 组件属性
3、部分字段引用不正确
4、主逻辑可靠性存在一定问题：
processHourlyRankSettle 结算方法幂等性存在一定问题insertCpHourlyWinner方法不可重入，表结构设计的时候应该增加多seq去重字段，插入记录已存在时忽略






提示词改进：
简化 照组件属性编码规范文档.md

