# CP小时第一名玩法组件 (新版)

## 组件概述

CP小时第一名玩法组件(ComponentId: 5159)用于实现每小时CP榜单第一名的记录、奖励发放和广播功能。

## 功能特性

### 1. 榜单结束事件结算
- 监听`RankingTimeEnd`事件，每小时结束时自动处理
- 记录CP榜单第一名信息
- 根据分数区间发放不同奖励
- 支持礼物奖池限额控制（使用long类型避免浮点数精度问题）
- 奖池不足时自动切换替代奖励（夏日飞骏进场秀3天）

### 2. 应援口令抽奖
- 对第一名CP所在房间发送应援口令"夏日派对，浪漫加倍"
- 集成`ChannelWatchwordLotteryComponent`组件实现抽奖功能
- 支持配置口令过期时间

### 3. 全服广播
- 广播第一名CP信息，包括用户头像、昵称、主播头像、昵称
- 支持多种广播模板和业务ID配置

### 4. HTTP接口
- 查询历史小时冠军CP接口
- 查询全服礼物奖池余额接口

## 数据库设计

### 表结构：cmpt_5159_cp_hourly_winner

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| act_id | bigint(20) | 活动ID |
| cmpt_use_inx | bigint(20) | 组件使用索引 |
| user_uid | bigint(20) | 用户UID |
| anchor_uid | bigint(20) | 主播UID |
| user_nick | varchar(255) | 用户昵称 |
| user_avatar | varchar(500) | 用户头像 |
| anchor_nick | varchar(255) | 主播昵称 |
| anchor_avatar | varchar(500) | 主播头像 |
| cp_score | bigint(20) | CP分数 |
| hour_time | varchar(10) | 小时时间戳(yyyyMMddHH) |
| award_status | tinyint(4) | 奖励发放状态 |
| award_content | text | 奖励内容JSON |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

## 组件配置

### 主要配置项

```java
@ComponentAttrField(labelText = "CP榜单ID")
private long cpRankId;

@ComponentAttrField(labelText = "CP阶段ID")
private long cpPhaseId;

@ComponentAttrField(labelText = "发奖业务ID", dropDownSourceBeanClass = BizSource.class)
private long awardBusiId;

@ComponentAttrField(labelText = "分数区间奖励配置", remark = "根据第一名分数区间发放不同奖励",
        subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = ScoreRangeAward.class))
private List<ScoreRangeAward> scoreRangeAwards;

@ComponentAttrField(labelText = "全服礼物奖池总限量", remark = "单位：分，避免浮点数精度问题")
private long totalGiftPoolLimit = 4000000L; // 40000.0元

@ComponentAttrField(labelText = "当前礼物奖池余额", remark = "单位：分，避免浮点数精度问题")
private long currentGiftPoolBalance = 1296000L; // 12960.0元

@ComponentAttrField(labelText = "奖池不足时替代奖励")
private AwardAttrConfig fallbackAward;

@ComponentAttrField(labelText = "应援口令抽奖组件索引")
private long watchwordLotteryComponentIndex;

@ComponentAttrField(labelText = "应援口令文案", defaultValue = "夏日派对，浪漫加倍")
private String watchwordText = "夏日派对，浪漫加倍";
```

### ScoreRangeAward配置项

```java
@ComponentAttrField(labelText = "最小分数")
private Long minScore;

@ComponentAttrField(labelText = "最大分数", remark = "0表示无上限")
private Long maxScore;

@ComponentAttrField(labelText = "奖励奖池ID")
private Long tAwardTskId;

@ComponentAttrField(labelText = "奖励奖包ID", remark = "填0，则是抽奖")
private Long tAwardPkgId;

@ComponentAttrField(labelText = "奖励发放数量")
private Integer num;

@ComponentAttrField(labelText = "奖励名称")
private String awardName;

@ComponentAttrField(labelText = "奖励图标")
private String awardIcon;

@ComponentAttrField(labelText = "奖励金额", remark = "单位：分")
private Long awardAmount;
```

## HTTP接口

### 1. 查询历史小时冠军CP

**接口地址**: `GET /5159/queryHistoryWinners`

**请求参数**:
- `actId`: 活动ID
- `cmptInx`: 组件索引
- `date`: 日期 (格式: yyyyMMdd)

**返回结果**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "userNick": "用户昵称",
      "userAvatar": "用户头像URL",
      "anchorNick": "主播昵称", 
      "anchorAvatar": "主播头像URL",
      "hourTime": "2025072114",
      "cpScore": 12345
    }
  ]
}
```

### 2. 查询全服礼物奖池余额

**接口地址**: `GET /5159/queryGiftPoolBalance`

**请求参数**:
- `actId`: 活动ID
- `cmptInx`: 组件索引

**返回结果**:
```json
{
  "code": 0,
  "msg": "success", 
  "data": {
    "totalLimit": 40000.0,
    "currentBalance": 12960.0,
    "usedAmount": 27040.0
  }
}
```

## 部署步骤

1. **执行数据库建表SQL**:
   ```sql
   -- 执行 docs/sql/cmpt_5159_cp_hourly_winner.sql
   ```

2. **在活动配置中添加组件**:
   - ComponentId设置为`5159`
   - 配置组件属性，包括榜单ID、奖励配置等

3. **确保依赖组件已配置**:
   - `ChannelWatchwordLotteryComponent`组件已正确配置

## 配置示例

```json
{
  "cpRankId": 1001,
  "cpPhaseId": 2001,
  "awardBusiId": 500,
  "scoreRangeAwards": [
    {
      "minScore": 0,
      "maxScore": 1000,
      "tAwardTskId": 10001,
      "tAwardPkgId": 20001,
      "num": 1,
      "awardName": "初级奖励",
      "awardIcon": "https://example.com/icon1.png",
      "awardAmount": 100000,
      "remark": "1000分以下奖励"
    },
    {
      "minScore": 1001,
      "maxScore": 0,
      "tAwardTskId": 10002,
      "tAwardPkgId": 20002,
      "num": 1,
      "awardName": "高级奖励",
      "awardIcon": "https://example.com/icon2.png",
      "awardAmount": 200000,
      "remark": "1000分以上奖励"
    }
  ],
  "totalGiftPoolLimit": 4000000,
  "currentGiftPoolBalance": 1296000,
  "fallbackAward": {
    "tAwardTskId": 10003,
    "tAwardPkgId": 20003,
    "num": 1,
    "awardName": "夏日飞骏进场秀",
    "awardIcon": "https://example.com/fallback.png",
    "awardAmount": 0,
    "remark": "奖池不足时的替代奖励"
  },
  "watchwordLotteryComponentIndex": 2062,
  "watchwordText": "夏日派对，浪漫加倍",
  "watchwordExpireMinutes": 60,
  "broadcastBusiId": 2,
  "broadcastTemplate": 3,
  "broadcastBannerId": 5159001
}
```

## 注意事项

1. **数据类型**: 礼物奖池金额使用long类型存储（单位：分），避免浮点数精度问题
2. **事件处理**: 实现了幂等性，支持事件重试
3. **防重机制**: 使用事件序列号进行防重检查
4. **异常处理**: 完善的异常处理和日志记录
5. **依赖组件**: 需要确保ChannelWatchwordLotteryComponent组件正常工作

## 测试建议

建议编写单元测试来验证以下功能：
1. 榜单结束事件处理逻辑
2. 奖励发放机制
3. 礼物奖池余额管理
4. HTTP接口响应
5. 异常情况处理
