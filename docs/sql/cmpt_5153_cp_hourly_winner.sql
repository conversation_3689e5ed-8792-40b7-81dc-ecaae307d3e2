-- CP小时第一名记录表
CREATE TABLE `cmpt_5153_cp_hourly_winner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `act_id` bigint(20) NOT NULL COMMENT '活动ID',
  `cmpt_use_inx` bigint(20) NOT NULL COMMENT '组件使用索引',
  `user_uid` bigint(20) NOT NULL COMMENT '用户UID',
  `anchor_uid` bigint(20) NOT NULL COMMENT '主播UID',
  `user_nick` varchar(255) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `user_avatar` varchar(500) NOT NULL DEFAULT '' COMMENT '用户头像',
  `anchor_nick` varchar(255) NOT NULL DEFAULT '' COMMENT '主播昵称',
  `anchor_avatar` varchar(500) NOT NULL DEFAULT '' COMMENT '主播头像',
  `cp_score` bigint(20) NOT NULL DEFAULT '0' COMMENT 'CP分数',
  `hour_time` varchar(10) NOT NULL DEFAULT '' COMMENT '小时时间戳(格式：yyyyMMddHH)',
  `award_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '奖励发放状态 0-未发放 1-已发放',
  `award_content` text COMMENT '奖励内容JSON',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_act_cmpt_hour_cp` (`act_id`, `cmpt_use_inx`, `hour_time`),
  KEY `idx_act_cmpt_date` (`act_id`, `cmpt_use_inx`, `hour_time`),
  KEY `idx_user_anchor` (`user_uid`, `anchor_uid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CP小时第一名记录表';
