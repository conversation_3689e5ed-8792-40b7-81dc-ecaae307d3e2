-- CP小时第一名记录表
CREATE TABLE `cmpt_5159_cp_hourly_winner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `act_id` bigint(20) NOT NULL COMMENT '活动ID',
  `cmpt_use_inx` int(11) NOT NULL COMMENT '组件使用索引',
  `seq` varchar(100) NOT NULL DEFAULT '' COMMENT '幂等性序列号',
  `hour_code` varchar(10) NOT NULL DEFAULT '' COMMENT '小时编码，格式：yyyyMMddHH',
  `user_uid` bigint(20) NOT NULL COMMENT '用户UID',
  `anchor_uid` bigint(20) NOT NULL COMMENT '主播UID',
  `user_nick` varchar(255) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `anchor_nick` varchar(255) NOT NULL DEFAULT '' COMMENT '主播昵称',
  `user_avatar` varchar(500) NOT NULL DEFAULT '' COMMENT '用户头像',
  `anchor_avatar` varchar(500) NOT NULL DEFAULT '' COMMENT '主播头像',
  `score` bigint(20) NOT NULL DEFAULT '0' COMMENT '第一名分数',
  `sid` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间SID',
  `ssid` bigint(20) NOT NULL DEFAULT '0' COMMENT '房间SSID',
  `gift_pool_remain` bigint(20) NOT NULL DEFAULT '0' COMMENT '奖池剩余金额（分为单位）',
  `award_amount` bigint(20) NOT NULL DEFAULT '0' COMMENT '实际发放奖励金额（分为单位）',
  `award_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '奖励类型：1-礼物奖励，2-进场秀奖励',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_act_cmpt_seq` (`act_id`, `cmpt_use_inx`, `seq`),
  UNIQUE KEY `uk_act_cmpt_hour` (`act_id`, `cmpt_use_inx`, `hour_code`),
  KEY `idx_act_cmpt_date` (`act_id`, `cmpt_use_inx`, `hour_code`),
  KEY `idx_user_anchor` (`user_uid`, `anchor_uid`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CP小时第一名记录表';
