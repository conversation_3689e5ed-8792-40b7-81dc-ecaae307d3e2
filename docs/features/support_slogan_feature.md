# 应援口令功能详解

## 功能概述

应援口令功能是CP小时第一名玩法组件的重要组成部分，当每小时CP榜单第一名产生时，系统会自动向第一名CP所在的房间发送应援口令，房间内的用户可以通过发送指定口令参与抽奖。

## 实现流程

### 1. 触发条件
- 每小时CP榜单结束
- 产生第一名CP
- 完成奖励发放后触发

### 2. 执行步骤

#### 2.1 配置验证
```java
if (StringUtils.isBlank(supportSlogan) || componentIndex <= 0) {
    log.warn("Invalid support slogan config");
    return;
}
```
- 检查应援口令文案是否配置
- 检查ChannelWatchwordLotteryComponent组件索引是否有效

#### 2.2 获取房间信息
```java
ChannelInfoVo channelInfo = getAnchorChannelInfo(cpUid.getAnchorUid());
```

**多重获取策略**：
1. **在线频道服务优先**：`OnlineChannelService.getChannelInfoVo(anchorUid)`
   - 获取主播当前开播的频道信息
   - 数据实时性高，准确度高

2. **用户当前频道降级**：`CommonService.getNoCacheUserCurrentChannel(anchorUid, 2)`
   - 当在线频道服务无数据时使用
   - 支持重试机制（最多2次）
   - 获取用户当前所在频道

#### 2.3 生成唯一序列号
```java
String seq = generateSupportSloganSeq(attr, cpUid, channelInfo);
```

**序列号格式**：
```
cp_hourly_winner_{actId}_{cmptUseInx}_{hourCode}_{anchorUid}_{sid}_{ssid}
```

**示例**：
```
cp_hourly_winner_2025072101_1_2025072114_123456_789012_345678
```

#### 2.4 设置过期时间
```java
Date expiredTime = DateUtil.addMinutes(new Date(), 30);
```
- 默认30分钟过期
- 可根据需要调整过期时间

#### 2.5 创建应援口令抽奖
```java
channelWatchwordLotteryComponent.addWatchwordLotteryBox(
    attr.getActId(),
    componentIndex,
    seq,
    cpUid.getMember(),
    channelInfo.getSid(),
    channelInfo.getSsid(),
    expiredTime
);
```

## 配置说明

### 组件属性配置

```java
@ComponentAttrField(labelText = "应援口令文案")
private String supportSlogan = "夏日派对，浪漫加倍";

@ComponentAttrField(labelText = "应援口令抽奖组件索引")
private long watchwordLotteryComponentIndex;
```

### 配置示例

```json
{
  "supportSlogan": "夏日派对，浪漫加倍",
  "watchwordLotteryComponentIndex": 1
}
```

## 依赖组件配置

### ChannelWatchwordLotteryComponent配置

需要确保对应的ChannelWatchwordLotteryComponent组件已正确配置：

```json
{
  "busiId": 200,
  "chatText": "夏日派对，浪漫加倍",
  "targetWord": "夏日派对浪漫加倍",
  "tAwardTskId": 100001,
  "singleLottery": false
}
```

**配置说明**：
- `chatText`: 显示给用户的口令文案
- `targetWord`: 用于匹配的关键词（去除标点符号）
- `tAwardTskId`: 抽奖奖池ID
- `singleLottery`: 是否每个口令只能抽一次

## 用户参与流程

### 1. 口令发布
- 系统自动在第一名CP所在房间创建应援口令抽奖
- 房间内用户可以看到应援口令提示

### 2. 用户参与
- 用户在房间内发送应援口令："夏日派对，浪漫加倍"
- 系统自动识别并触发抽奖

### 3. 抽奖处理
- 系统验证用户是否已参与过该口令抽奖
- 执行抽奖逻辑，发放奖励
- 记录抽奖结果

## 技术实现细节

### 1. 房间信息获取

```java
private ChannelInfoVo getAnchorChannelInfo(long anchorUid) {
    // 1. 优先从在线频道服务获取
    ChannelInfoVo channelInfo = onlineChannelService.getChannelInfoVo(anchorUid);
    if (channelInfo != null && channelInfo.getSid() > 0) {
        return channelInfo;
    }
    
    // 2. 降级到用户当前频道
    UserCurrentChannel userChannel = commonService.getNoCacheUserCurrentChannel(anchorUid, 2);
    if (userChannel != null && userChannel.getTopsid() > 0) {
        // 转换为ChannelInfoVo
        return convertToChannelInfoVo(userChannel);
    }
    
    return null;
}
```

### 2. 错误处理

```java
try {
    // 应援口令发送逻辑
} catch (Exception e) {
    log.error("Failed to send support slogan to room", e);
    // 不抛出异常，避免影响主流程
}
```

**设计原则**：
- 应援口令发送失败不影响主要的奖励发放流程
- 记录详细的错误日志便于问题排查
- 支持重试和降级策略

### 3. 幂等性保证

- 使用唯一的序列号确保同一小时同一CP不会重复创建应援口令
- 序列号包含时间、活动、组件、CP等信息确保唯一性

## 监控和日志

### 关键日志

```java
// 成功日志
log.info("Support slogan sent successfully: slogan={}, seq={}, sid={}, ssid={}, anchorUid={}");

// 警告日志
log.warn("Invalid support slogan config: slogan={}, componentIndex={}");
log.warn("Failed to get anchor channel info: anchorUid={}, channelInfo={}");

// 错误日志
log.error("Failed to send support slogan to room: cpUid={}", JSON.toJSONString(cpUid), e);
```

### 监控指标建议

1. **成功率监控**
   - 应援口令发送成功率
   - 房间信息获取成功率

2. **性能监控**
   - 房间信息获取耗时
   - 应援口令创建耗时

3. **业务监控**
   - 每小时应援口令创建数量
   - 用户参与应援口令抽奖数量

## 故障排查

### 常见问题

1. **房间信息获取失败**
   - 检查主播是否在线
   - 检查OnlineChannelService服务状态
   - 检查CommonService服务状态

2. **应援口令创建失败**
   - 检查ChannelWatchwordLotteryComponent组件配置
   - 检查组件索引是否正确
   - 检查数据库连接状态

3. **用户无法参与抽奖**
   - 检查口令文案配置是否一致
   - 检查targetWord配置是否正确
   - 检查抽奖奖池配置

### 调试方法

1. **查看日志**
   ```bash
   grep "Support slogan" application.log
   grep "Failed to send support slogan" application.log
   ```

2. **检查数据库**
   ```sql
   SELECT * FROM cmpt_2062_lottery_box WHERE seq LIKE 'cp_hourly_winner_%';
   SELECT * FROM cmpt_2062_lottery_record WHERE box_id IN (SELECT id FROM cmpt_2062_lottery_box WHERE seq LIKE 'cp_hourly_winner_%');
   ```

3. **验证配置**
   - 检查组件配置是否正确
   - 验证依赖组件是否正常工作

## 扩展功能

### 未来可能的增强

1. **动态口令**
   - 支持根据CP信息生成个性化口令
   - 支持多种口令模板

2. **奖励层级**
   - 根据参与时间设置不同奖励
   - 支持限量奖励

3. **统计分析**
   - 应援口令参与度统计
   - 用户行为分析
